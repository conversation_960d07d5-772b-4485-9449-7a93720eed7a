<?php
$selected_products = get_field('product_category');

if ($selected_products) {
    $args = array(
        'post_type' => 'product',
        'post__in' => $selected_products, // Use the IDs returned by ACF
        'posts_per_page' => -1,
        'orderby' => 'post__in', // Maintain the same order as selected
    );

    $loop = new WP_Query($args);

    if ($loop->have_posts()) : ?>
        <div class="swiper-container product-carousel woocommerce">
            <div class="swiper-wrapper">
                <?php while ($loop->have_posts()) : $loop->the_post(); ?>
                    <div class="swiper-slide product-slide">
                        <?php
                        $product_id = get_the_ID();
                        $wishlist = isset($_COOKIE['smort_wishlist']) ? explode(',', $_COOKIE['smort_wishlist']) : [];
                        $active = in_array($product_id, $wishlist) ? ' active' : '';
                        ?>
                        <div class="smort-heart-wrapper">
                            <button class="smort-heart-btn<?php echo esc_attr($active); ?>" data-product-id="<?php echo esc_attr($product_id); ?>" aria-label="Önskelista">
                                <svg class="smort-heart-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 
                     2 5.42 4.42 3 7.5 3c1.74 0 3.41 0.81 
                     4.5 2.09C13.09 3.81 14.76 3 16.5 3 
                     19.58 3 22 5.42 22 8.5c0 3.78-3.4 
                     6.86-8.55 11.54L12 21.35z"
                                        fill="none"
                                        stroke="black"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round" />
                                </svg>
                            </button>
                        </div>

                        <a href="<?php the_permalink(); ?>">
                            <?php woocommerce_show_product_sale_flash(); ?>

                            <?php
                            global $product;

                            // Hämta lagerstatus
                            $stock_status = $product->get_stock_status();
                            $backorders = $product->backorders_allowed();
                            $stock_text = '';
                            $stock_class = '';

                            if ($stock_status === 'instock' && !$backorders) {
                                $stock_text = 'Leverans 1-2 dagar';
                                $stock_class = 'stock-green';
                            } elseif ($stock_status === 'onbackorder') {
                                $stock_text = 'Leverans 6-10 dagar';
                                $stock_class = 'stock-yellow';
                            } else {
                                $stock_text = 'Slut i lager';
                                $stock_class = 'stock-red';
                            }

                            if (has_post_thumbnail($product->get_id())) {
                                $main_image = wp_get_attachment_image(get_post_thumbnail_id($product->get_id()), 'full');
                            } else {
                                $main_image = '<img class="placeholder" src="' . wc_placeholder_img_src() . '" alt="Placeholder" />';
                            }
                            $gallery_images = $product->get_gallery_image_ids();
                            if (!empty($gallery_images)) {
                                $hover_image = wp_get_attachment_image($gallery_images[0], 'full');
                            } else {
                                $hover_image = $main_image;
                            }
                            ?>


                            <div class="product-image-container">
                                <div class="first-image">
                                    <?php echo $main_image; ?>
                                </div>
                                <div class="hover-image">
                                    <?php echo $hover_image; ?>
                                </div>
                            </div>
                            <div class="product-info-slider">
                                <div class="title-stock-div">
                                    <h2 class="woocommerce-loop-product__title"><?php the_title(); ?></h2>
                                    <div class="stock-status <?php echo esc_attr($stock_class); ?>">
                                        <span class="stock-dot"></span> <?php echo esc_html($stock_text); ?>
                                    </div>
                                </div>
                                <div class="cta-div">
                                    <span class="price"><?php woocommerce_template_loop_price(); ?></span>
                                    <a class="product-slider-cta" href="<?php the_permalink(); ?>">Till produkten</a>
                                </div>
                            </div>
                        </a>
                    </div>
                <?php endwhile; ?>
            </div>
            <div class="pagination-wrapper">
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
<?php endif;

    wp_reset_postdata();
}
?>