.product-carousel {
  padding: 0px;
  overflow: hidden;
}

.swiper-wrapper {
  display: flex;
}

.product-slide {
  border-radius: 00px;
}

.product-category {
  margin-top: 5px;
  font-size: 0.9em;
  color: #555;
}

.price {
  margin-top: 0px;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  gap: 5px;
}

.cta-div {
  padding-bottom: 20px;
}
/* Swiper navigation buttons */

/* Wrapper för navigationspilar */
/* Föräldrakontainer som håller både swiper och navigation */
.swiper-carousel-wrapper {
  position: relative; /* Viktigt för att pilarna ska placeras rätt */
  overflow: hidden; /* Beh<PERSON>ll hidden här för att förhindra sidescroll */
}

/* Navigeringspilar */
.swiper-navigation {
  position: absolute;
  width: 100%; /* Full bredd för att centrera pilarna */
  bottom: -40px; /* Placera nedanför ka<PERSON>ellen */
  display: flex;
  justify-content: center; /* Centrera pilarna horisontellt */
  z-index: 10; /* <PERSON><PERSON><PERSON> så att de syns ovanpå */
}

.swiper-button-next,
.swiper-button-prev {
  position: relative; /* Behöver inte vara absolut eftersom de centreras i sin wrapper */
  color: #ff6600;
  border: 1px solid #ff6600;
  border-radius: 0px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin: 0 10px; /* Avstånd mellan pilarna */
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
}

/* Hover-effekt för pilar */
.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: #ff6600;
  color: #fff;
  border-color: #ff6600;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 10px;
}
.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 15px;
}

/* titles */
h2.woocommerce-loop-product__title {
  font-size: 15px;
  color: #000;
  line-height: 1.5;
  margin-top: 0px;
  max-width: 80%;
  display: block;
  margin-bottom: 0px;
}
.product-category a {
  color: var(--accentColor3);
  margin-bottom: 0px;
  display: block;
}
.product-slide .amount {
  font-size: 20px;
  margin-top: 15px !important;
  display: block;
  margin-top: 0px !important;
}
.swiper-slide span.price bdi {
  font-size: 19px;
  text-decoration: none;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}

span.product-parent-category {
  font-size: 12px;
  color: #000;
}

/* Dots */

/* Swiper pagination dots */
.swiper-pagination-bullet {
  background: #333;
  opacity: 0.7;
}

.swiper-pagination-bullet-active {
  background: #4c8077; /* Active dot color */
  opacity: 1;
}

/* Adjust the position and appearance of the dots if needed */
.swiper-pagination {
  margin-top: 15px;
  text-align: center;
  bottom: -30px !important;
}
.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
  width: 20px;
  border-radius: 5px;
}
.product-varumarke {
  color: #000;
  margin-top: 10px;
}
span.onsale {
  position: absolute;
  top: 20px;
  right: 20px;
  color: #ffffff;
  background-color: var(--accentColor);
  /* width: 50px; */
  /* height: 50px; */
  border-radius: 30px;
  padding: 10px 10px;
  font-size: 15px;
}
.price del bdi {
  color: #8e8e8e;
  font-size: 19px;
}

/* Ensure the star rating container has the correct display and positioning */
.woocommerce .star-rating {
  display: inline-block;
  position: relative;
  font-size: 1.2em; /* Adjust as necessary */
  color: #ffcc00; /* Color for the filled stars */
  line-height: 1; /* Make sure the stars are aligned */
  z-index: 10; /* Ensure the stars are above other elements */
}

/* Unfilled star background */
.woocommerce .star-rating::before {
  content: "★★★★★"; /* Five unfilled stars */
  color: #ccc; /* Color for unfilled stars */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1; /* Ensure unfilled stars are below the filled stars */
}

/* Filled stars */
.woocommerce .star-rating span {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  overflow: hidden;
  color: #ffcc00; /* Color for filled stars */
  z-index: 2; /* Ensure filled stars are above the unfilled stars */
}

/* Ensure the filled stars' width reflects the rating */
.woocommerce .star-rating span::before {
  content: "★★★★★"; /* Five filled stars */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3; /* Ensure filled stars are above the unfilled stars */
}

/* Ensure the star rating div has the proper z-index */
.woocommerce-loop-rating {
  position: relative;
  z-index: 9999; /* High z-index to ensure it's above other elements */
}

/* Ensure parent containers don't hide overflow */
.swiper-slide,
.product-slide {
  overflow: visible; /* Ensure that overflow isn't cutting off content */
}

/* Swiper slide */

.swiper-slide {
  height: auto;
  display: flex;
  flex-direction: column;
}
.swiper-wrapper {
  display: flex;
  align-items: stretch;
}
a.product-slider-cta {
  text-align: center !important;
  display: block;
  background-color: var(--accentColor);
  color: #fff;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  padding: 10px;
  width: calc(100% - 12px);
  margin-top: 15px;
}
.swiper-slide h2.woocommerce-loop-product__title {
  max-width: 100%;
  font-size: 1.7rem;
  padding-top: 20px;
}
.swiper-slide span.price ins {
  text-decoration: none;
}

.product-slider-cta img {
  width: 50px;
  height: 50px;
  object-fit: contain;
  margin: 0px;
  border: 0px !important;
}

.product-slide img {
  width: calc(100% - 20px);
  height: 100%;
  object-fit: contain;
  margin: 10px;
  border-radius: 0px !important;
  background-color: #fff;
  border: 1px solid #e0e0e0;
}
.product-slide img.placeholder {
  object-position: bottom !important;
}

/* Arrow */

img.cta-arrow {
  width: 20px;
  height: 20px;
  margin: 0px;
  margin-top: 5px;
}

/* Container för produktbilder */
.product-image-container {
  position: relative;
  width: 100%;
  height: 320px;
  z-index: 1; 
}

.product-image-container .first-image,
.product-image-container .hover-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease-in-out;
  z-index: 1;
}



.product-image-container .hover-image {
  opacity: 0; 
}

.product-image-container:hover .hover-image {
  opacity: 1; 
}

.product-image-container:hover .first-image {
  opacity: 0; /
}

.product-slide h2.woocommerce-loop-product__title,
.product-slide .cta-div {
  position: relative;
  z-index: 2;
}

.title-stock-div {
  align-items: center;
  padding-top: 20px;
}

.product-info-slider {
  padding: 0px 16px;
}
.smort-heart-wrapper {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 999;
}