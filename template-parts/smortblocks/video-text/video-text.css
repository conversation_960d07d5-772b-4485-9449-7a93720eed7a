/* För containern med videon */
.blend-mode-container {
  position: relative; /* Behövs för isolation och z-index */
  height: 600px; /* Sektionens höjd */
  display: flex; /* Flexbox för textcentrering */
  flex-direction: column; /* Lägg elementen i en kolumn */
  justify-content: flex-start; /* Börjar högst upp */
  align-items: center; /* Centrerar alla element horisontellt */
  isolation: isolate; /* Viktigt för mix-blend-mode */
  overflow: visible; /* Till<PERSON>ter innehållet att synas utanför containerns gränser */
}

/* Bakgrundsvideon */
.background-video {
  position: absolute; /* Placera videon i bakgrunden */
  top: 0;
  left: 0;
  width: 100%; /* Täcker hela bredden */
  height: 100%; /* Täcker hela höjden */
  object-fit: cover; /* Behåll proportioner och fyll containern */
  z-index: 0; /* Ligger under texten */
}

/* För rubriken */
.blend-mode-text {
  color: #ffffff; /* Vit text som grund */
  font-size: 8rem; /* Storlek på rubriken */
  line-height: 1; /* Radavstånd */
  text-align: center; /* Centrerad text */
  mix-blend-mode: difference; /* Skapar kontrasteffekt */
  z-index: 1; /* Se till att texten ligger ovanpå videon */
  position: relative; /* Behåll textens position i förhållande till bakgrunden */
  margin-top: -100px;
  margin-bottom: 0px;
  max-width: 1000px;
}

/* För paragrafen */
.blend-mode-paragraph {
  color: #ffffff; /* Vit text som grund */
  font-size: 2rem; /* Mindre storlek än rubriken */
  text-align: center; /* Centrerad text */
  mix-blend-mode: difference; /* Skapar kontrasteffekt */
  z-index: 1; /* Se till att texten ligger ovanpå videon */
  position: relative; /* Behåll textens position */
  margin: 10px 0 0; /* Utrymme ovanför och under */
}

/* För länken */
.blend-mode-link {
  color: #ffffff;
  font-size: 17px;
  text-align: center;
  mix-blend-mode: difference;
  z-index: 1;
  position: relative;
  margin-top: 10px;
  text-decoration: none;
  border: 1px solid;
  transition: all 0.3s ease;
  text-transform: uppercase;
  padding: 13px;
  font-family: 'CustomHeadingFont';
  min-width: 200px;
  text-align: left;
  margin-top: 20px;
}

.blend-mode-link:after {
  content: var(--arrowRight);
  position: absolute;
  right: 9px;
  top: 13px;
  transition: transform 0.2s ease-in-out;
}

.blend-mode-link:hover {
  color: #ff9900; /* Ändra färg vid hovring */
  border-bottom: 1px solid #ff9900; /* Ändra linjens färg vid hovring */
}

@media screen and (max-width: 992px) {
.blend-mode-text{
  font-size: 4rem;
}
.blend-mode-container{
   height: 450px;
}
.blend-mode-text{
  margin-top: -50px; 
}
}