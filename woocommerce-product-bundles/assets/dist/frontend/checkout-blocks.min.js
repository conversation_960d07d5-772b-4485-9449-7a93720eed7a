(()=>{"use strict";const i=window.wp.data;var d=window.wp.hooks;function e(d,e){return!!d&&!e?.extensions?.bundles?.bundled_by&&d}(0,window.wc.blocksCheckout.registerCheckoutFilters)("product-bundles",{cartItemClass:(d,{bundles:e},{context:_,cartItem:i})=>{var t;return e&&(t=[],e.bundled_by?(t.push("is-bundled"),t.push("is-bundled__cid_"+e.bundled_item_data.bundle_id),t.push("is-bundled__iid_"+e.bundled_item_data.bundled_item_id),e.bundled_item_data.is_indented&&t.push("is-bundled__indented"),e.bundled_item_data.is_last&&t.push("is-bundled__last"),e.bundled_item_data.is_removable&&t.push("is-bundled__removable"),e.bundled_item_data.is_subtotal_aggregated&&t.push("is-bundled__subtotal_aggregated"),e.bundled_item_data.is_price_hidden&&t.push("is-bundled__price_hidden"),e.bundled_item_data.is_subtotal_hidden&&t.push("is-bundled__subtotal_hidden"),e.bundled_item_data.is_hidden_in_cart&&"cart"===_&&t.push("is-bundled__hidden"),e.bundled_item_data.is_hidden_in_summary&&"summary"===_&&t.push("is-bundled__hidden"),e.bundled_item_data.is_thumbnail_hidden&&t.push("is-bundled__thumbnail_hidden"),e.bundled_item_data.is_parent_visible&&t.push("is-bundled__description_hidden"),e.bundled_item_data.is_composited&&t.push("is-bundled__composited"),e.bundled_item_data.is_ungrouped&&t.push("is-bundled__ungrouped")):e.bundled_items&&(t.push("is-bundle"),t.push("is-bundle__cid_"+i.id),e.bundle_data.is_editable&&t.push("is-bundle__editable"),e.bundle_data.is_hidden&&t.push("is-bundle__hidden"),e.bundle_data.is_title_hidden&&t.push("is-bundle__title_hidden"),e.bundle_data.is_price_hidden&&t.push("is-bundle__price_hidden"),e.bundle_data.is_subtotal_hidden&&t.push("is-bundle__subtotal_hidden"),e.bundle_data.is_meta_hidden_in_cart&&"cart"===_&&t.push("is-bundle__meta_hidden"),e.bundle_data.is_meta_hidden_in_summary)&&"summary"===_&&t.push("is-bundle__meta_hidden"),t.length)&&(d+=" "+t.join(" ")),d}}),(0,d.addAction)("experimental__woocommerce_blocks-cart-remove-item","product-bundles",function({product:d}){var e,_=(0,i.dispatch)("wc/store/cart")["itemIsPendingDelete"];e=_,_=(_=d)?.extensions?.bundles?.bundled_items,!Array.isArray(_)&&"object"!=typeof _||(Array.isArray(_)?_:Object.values(_)).forEach(d=>e(d,!0))}),(0,d.addFilter)("woocommerce_show_cart_item_removed_notice","woocommerce-blocks/checkout",e),(0,d.addFilter)("woocommerce_show_cart_item_quantity_changed_notice","woocommerce-blocks/checkout",e)})();