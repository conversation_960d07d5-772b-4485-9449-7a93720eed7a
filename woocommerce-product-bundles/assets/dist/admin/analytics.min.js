(()=>{var r={703:(e,t,r)=>{"use strict";var n=r(414);function a(){}function o(){}o.resetWarningCache=a,e.exports=function(){function e(e,t,r,a,o,s){if(s!==n)throw(s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")).name="Invariant Violation",s}function t(){return e}var r={array:e.isRequired=e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:a};return r.PropTypes=r}},697:(e,t,r)=>{e.exports=r(703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},a={};function B(e){var t=a[e];return void 0!==t||(t=a[e]={exports:{}},r[e](t,t.exports,B)),t.exports}B.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return B.d(t,{a:t}),t},B.d=(e,t)=>{for(var r in t)B.o(t,r)&&!B.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},B.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);(()=>{"use strict";const I=window.wp.hooks,V=window.wp.i18n,A=window.React,Q=window.wp.element,j=window.wc.components,d=window.wc.wcSettings;var e=window.wp.compose,t=window.wp.data,r=B(697),r=B.n(r);const H=window.wc.navigation,y=window.wc.number,M=window.wc.data,_=window.wc.date;var a=window.wc.currency;const o=B.n(a)()(d.CURRENCY);a=(0,Q.createContext)(o);class s extends Q.Component{formatVal(e,t){var{formatAmount:r,getCurrencyConfig:a}=this.context;return"currency"===t?r(e):(0,y.formatValue)(a(),t,e)}getValues(e,t){var{emptySearchResults:r,summaryData:a}=this.props,a=a["totals"],o=a.primary?a.primary[e]:0,a=a.secondary?a.secondary[e]:0,e=r?0:o,o=r?0:a;return{delta:(0,y.calculateDelta)(e,o),prevValue:this.formatVal(o,t),value:this.formatVal(e,t)}}render(){const{charts:e,query:t,selectedChart:i,summaryData:r,defaultDateRange:a}=this.props;var{isError:o,isRequesting:s}=r;if(!o){if(s)return(0,A.createElement)(j.SummaryListPlaceholder,{numberOfItems:e.length});const c=(0,_.getDateParamsFromQuery)(t,a)["compare"];return(0,A.createElement)(j.SummaryList,null,({onToggle:l})=>e.map(e=>{var{key:e,order:t,orderby:r,label:a,type:o}=e,s={chart:e},r=(r&&(s.orderby=r),t&&(s.order=t),(0,H.getNewPath)(s)),t=i.key===e,{delta:s,prevValue:o,value:n}=this.getValues(e,o);return(0,A.createElement)(j.SummaryNumber,{key:e,delta:s,href:r,label:a,prevLabel:"previous_period"===c?(0,V.__)("Previous Period:","woocommerce-product-bundles"):(0,V.__)("Previous Year:","woocommerce-product-bundles"),prevValue:o,selected:t,value:n,onLinkClickCallback:()=>{l&&l()}})}))}}}s.propTypes={charts:r().array.isRequired,endpoint:r().string.isRequired,query:r().object.isRequired,selectedChart:r().shape({key:r().string.isRequired,label:r().string.isRequired,order:r().oneOf(["asc","desc"]),orderby:r().string,type:r().oneOf(["average","number","currency"]).isRequired}).isRequired,summaryData:r().object,report:r().string},s.defaultProps={summaryData:{totals:{primary:{},secondary:{}},isError:!1}},s.contextType=a;const q=(0,e.compose)((0,t.withSelect)((e,t)=>{const{charts:r,endpoint:a,limitProperties:o,query:s,filters:n,advancedFilters:l}=t;var i,t=o||[a],c=t.some(e=>s[e]&&s[e].length);return s.search&&!c?{emptySearchResults:!0}:(c=r&&r.map(e=>e.key),i=e(M.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings")["woocommerce_default_date_range"],{summaryData:(0,M.getSummaryNumbers)({endpoint:a,query:s,select:e,limitBy:t,filters:n,advancedFilters:l,defaultDateRange:i,fields:c}),defaultDateRange:i})}))(s),c=window.wp.date,Y=window.lodash;class n extends Q.Component{render(){var{className:e,isError:t,isEmpty:r}=this.props;let a,o,s,n;return t?(a=(0,V.__)("There was an error getting your stats. Please try again.","woocommerce-product-bundles"),o=(0,V.__)("Reload","woocommerce-product-bundles"),n=()=>{window.location.reload()}):r&&(a=(0,V.__)("No results could be found for this date range.","woocommerce-product-bundles"),o=(0,V.__)("View Orders","woocommerce-product-bundles"),s=(0,d.getAdminLink)("edit.php?post_type=shop_order")),(0,A.createElement)(j.EmptyContent,{className:e,title:a,actionLabel:o,actionURL:s,actionCallback:n})}}n.propTypes={className:r().string,isError:r().bool,isEmpty:r().bool},n.defaultProps={className:""};const U=n;class l extends Q.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,Y.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:a}=this.props;return e.data.intervals.map(function(e){const r={};return e.subtotals.segments.forEach(function(e){var t;e.segment_label&&(t=r[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label,r[e.segment_id]={label:t,value:e.subtotals[a.key]||0})}),{date:(0,c.format)("Y-m-d\\TH:i:s",e.date_start),...r}})}getTimeChartData(){const{query:a,primaryData:e,secondaryData:o,selectedChart:s,defaultDateRange:t}=this.props,n=(0,_.getIntervalForQuery)(a),{primary:l,secondary:i}=(0,_.getCurrentDates)(a,t);return e.data.intervals.map(function(e,t){var r=(0,_.getPreviousDate)(e.date_start,l.after,i.after,a.compare,n),t=o.data.intervals[t];return{date:(0,c.format)("Y-m-d\\TH:i:s",e.date_start),primary:{label:`${l.label} (${l.range})`,labelDate:e.date_start,value:e.subtotals[s.key]||0},secondary:{label:`${i.label} (${i.range})`,labelDate:r.format("YYYY-MM-DD HH:mm:ss"),value:t&&t.subtotals[s.key]||0}}})}getTimeChartTotals(){var{primaryData:e,secondaryData:t,selectedChart:r}=this.props;return{primary:(0,Y.get)(e,["data","totals",r.key],null),secondary:(0,Y.get)(t,["data","totals",r.key],null)}}renderChart(e,t,r,a){var{emptySearchResults:o,filterParam:s,interactiveLegend:n,itemsLabel:l,legendPosition:i,path:c,query:d,selectedChart:u,showHeaderControls:m,primaryData:p}=this.props,y=(0,_.getIntervalForQuery)(d),g=(0,_.getAllowedIntervalsForQuery)(d),p=(0,_.getDateFormatsForInterval)(y,p.data.intervals.length),o=o?(0,V.__)("No data for the current search","woocommerce-admin"):(0,V.__)("No data for the selected date range","woocommerce-admin"),{formatAmount:b,getCurrencyConfig:h}=this.context;return(0,A.createElement)(j.Chart,{allowedIntervals:g,data:r,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:o,filterParam:s,interactiveLegend:n,interval:y,isRequesting:t,itemsLabel:l,legendPosition:i,legendTotals:a,mode:e,path:c,query:d,screenReaderFormat:p.screenReaderFormat,showHeaderControls:m,title:u.label,tooltipLabelFormat:p.tooltipLabelFormat,tooltipTitle:"time-comparison"===e&&u.label||null,tooltipValueFormat:(0,M.getTooltipValueFormat)(u.type,b),chartType:(0,_.getChartTypeForQuery)(d),valueType:u.type,xFormat:p.xFormat,x2Format:p.x2Format,currency:h()})}renderItemComparison(){var{isRequesting:e,primaryData:t}=this.props;return t.isError?(0,A.createElement)(U,{isError:!0}):(e=e||t.isRequesting,t=this.getItemChartData(),this.renderChart("item-comparison",e,t))}renderTimeComparison(){var{isRequesting:e,primaryData:t,secondaryData:r}=this.props;return!t||t.isError||r.isError?(0,A.createElement)(U,{isError:!0}):(e=e||t.isRequesting||r.isRequesting,t=this.getTimeChartData(),r=this.getTimeChartTotals(),this.renderChart("time-comparison",e,t,r))}render(){var e=this.props["mode"];return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}l.contextType=a,l.propTypes={filters:r().array,isRequesting:r().bool,itemsLabel:r().string,limitProperties:r().array,mode:r().string,path:r().string.isRequired,primaryData:r().object,query:r().object.isRequired,secondaryData:r().object,selectedChart:r().shape({key:r().string.isRequired,label:r().string.isRequired,order:r().oneOf(["asc","desc"]),orderby:r().string,type:r().oneOf(["average","number","currency"]).isRequired}).isRequired},l.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const E=(0,e.compose)((0,t.withSelect)((e,t)=>{const{charts:r,endpoint:a,filters:o,isRequesting:s,limitProperties:n,query:l,advancedFilters:i}=t;var c,d,u=n||[a],m=function e(t,r,a={}){var o,s;return t&&0!==t.length?(s=(t=t.slice(0)).pop()).showFilters(r,a)?(o=(0,H.flattenFilters)(s.filters),s=r[s.param]||s.defaultValue||"all",(0,Y.find)(o,{value:s})):e(t,r,a):null}(o,l),p=(0,Y.get)(m,["settings","param"]),t=t.mode||function(e,t){if(e&&t){var r=(0,Y.get)(e,["settings","param"]);if(!r||Object.keys(t).includes(r))return(0,Y.get)(e,["chartMode"])}return null}(m,l)||"time-comparison",m=e(M.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings")["woocommerce_default_date_range"],y=e(M.REPORTS_STORE_NAME),p={mode:t,filterParam:p,defaultDateRange:m};return s?p:(c=u.some(e=>l[e]&&l[e].length),l.search&&!c?{...p,emptySearchResults:!0}:(c=r&&r.map(e=>e.key),d=(0,M.getReportChartData)({endpoint:a,dataType:"primary",query:l,select:e,selector:y,limitBy:u,filters:o,advancedFilters:i,defaultDateRange:m,fields:c}),"item-comparison"===t?{...p,primaryData:d}:(t=(0,M.getReportChartData)({endpoint:a,dataType:"secondary",query:l,select:e,selector:y,limitBy:u,filters:o,advancedFilters:i,defaultDateRange:m,fields:c}),{...p,primaryData:d,secondaryData:t})))}))(l);var i=window.wp.apiFetch,k=B.n(i);const D=window.wp.url;function u(e,t=[]){e=(0,Y.find)(t,{key:e});return e||t[0]}const g=window.wp.htmlEntities,K=window.wp.components,$=window.wp.dom,W=window.wc.csvExport,z=()=>(0,A.createElement)("svg",{role:"img","aria-hidden":"true",focusable:"false",version:"1.1",xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:"0 0 24 24"},(0,A.createElement)("path",{d:"M18,9c-0.009,0-0.017,0.002-0.025,0.003C17.72,5.646,14.922,3,11.5,3C7.91,3,5,5.91,5,9.5c0,0.524,0.069,1.031,0.186,1.519 C5.123,11.016,5.064,11,5,11c-2.209,0-4,1.791-4,4c0,1.202,0.541,2.267,1.38,3h18.593C22.196,17.089,23,15.643,23,14 C23,11.239,20.761,9,18,9z M12,16l-4-5h3V8h2v3h3L12,16z"}));i=n=>{const{getHeadersContent:a,getRowsContent:o,getSummary:s,isRequesting:e,primaryData:t,tableData:r,endpoint:l,itemIdField:i,tableQuery:L,compareBy:c,compareParam:d,searchBy:u,labels:m={},...p}=n,{query:y,columnPrefsKey:g}=n,{items:b,query:h}=r;var _=y[d]?(0,H.getIdsFromQuery)(y[c]):[];const[w,f]=(0,Q.useState)(_),v=(0,Q.useRef)(null),{updateUserPreferences:C,...R}=(0,M.useUserPreferences)();if(r.isError||t.isError)return(0,A.createElement)(U,{isError:!0});let S=[];g&&(S=R&&R[g]?R[g]:S);const q=(e,t,r)=>{r=s?s(t,r):null;return(0,I.applyFilters)("woocommerce_admin_report_table",{endpoint:l,headers:a(),rows:o(e),totals:t,summary:r,items:b})};const E=e=>{var t=n["ids"];f(e?t:[])},k=(e,t)=>{var r=n["ids"];t?f((0,Y.uniq)([r[e],...w])):(t=w.indexOf(r[e]),f([...w.slice(0,t),...w.slice(t+1)]))};var _=e||r.isRequesting||t.isRequesting,D=(0,Y.get)(t,["data","totals"],{}),T=b.totalResults||0,x=0<T,F=(0,H.getSearchWords)(y).map(e=>({key:e,label:e})),P=b["data"],P=q(P,D,T);let{headers:N,rows:B}=P;var O,D=P["summary"],P=(c&&(B=B.map((e,t)=>[(e=>{var{ids:t=[]}=n,t=-1!==w.indexOf(t[e]);return{display:(0,A.createElement)(K.CheckboxControl,{onChange:(0,Y.partial)(k,e),checked:t}),value:!1}})(t),...e]),N=[(()=>{var{ids:e=[]}=n,t=0<e.length,e=t&&e.length===w.length;return{cellClassName:"is-checkbox-column",key:"compare",label:(0,A.createElement)(K.CheckboxControl,{onChange:E,"aria-label":(0,V.__)("Select All"),checked:e,disabled:!t}),required:!0}})(),...N]),P=N,(O=S)?P.map(e=>({...e,visible:e.required||!O.includes(e.key)})):P.map(e=>({...e,visible:e.required||!e.hiddenByDefault})));return(0,A.createElement)(Q.Fragment,null,(0,A.createElement)("div",{className:"woocommerce-report-table__scroll-point",ref:v,"aria-hidden":!0}),(0,A.createElement)(j.TableCard,{className:"woocommerce-report-table-"+l.replace("/","-"),hasSearch:!!u,actions:[c&&(0,A.createElement)(j.CompareButton,{key:"compare",className:"woocommerce-table__compare",count:w.length,helpText:m.helpText||(0,V.__)("Check at least two items below to compare","woocommerce-admin"),onClick:()=>{c&&(0,H.onQueryChange)("compare")(c,d,w.join(","))},disabled:!x},m.compareButton||(0,V.__)("Compare","woocommerce-admin")),u&&(0,A.createElement)(j.Search,{allowFreeTextSearch:!0,inlineTags:!0,key:"search",onChange:e=>{var t=n["baseSearchQuery"],e=e.map(e=>e.label.replace(",","%2C"));e.length?(0,H.updateQueryString)({filter:void 0,[d]:void 0,[u]:void 0,...t,search:(0,Y.uniq)(e).join(",")}):(0,H.updateQueryString)({search:void 0})},placeholder:m.placeholder||(0,V.__)("Search by item name","woocommerce-admin"),selected:F,showClearButton:!0,type:u,disabled:!x}),x&&(0,A.createElement)(K.Button,{key:"download",className:"woocommerce-table__download-button",disabled:_,onClick:()=>{const{createNotice:t,startExport:e,title:r}=n;var a=Object.assign({},y),{data:o,totalResults:s}=b;delete a.extended_info,a.search&&delete a[u],o&&o.length===s?({headers:o,rows:s}=q(o,s),(0,W.downloadCSVFile)((0,W.generateCSVFileName)(r,a),(0,W.generateCSVDataFromTable)(o,s))):e(l,h).then(()=>t("success",(0,V.sprintf)((0,V.__)("Your %s Report will be emailed to you.","woocommerce-admin"),r))).catch(e=>t("error",e.message||(0,V.sprintf)((0,V.__)("There was a problem exporting your %s Report. Please try again.","woocommerce-admin"),r)))}},(0,A.createElement)(z,null),(0,A.createElement)("span",{className:"woocommerce-table__download-button__label"},m.downloadButton||(0,V.__)("Download","woocommerce-admin")))],headers:P,isLoading:_,onQueryChange:H.onQueryChange,onColumnsChange:(t,e)=>{var r=N.map(e=>e.key).filter(e=>!t.includes(e));g&&(r={[g]:r},C(r))},onSort:(e,t)=>{(0,H.onQueryChange)("sort")(e,t);l},onPageChange:(e,t)=>{v.current.scrollIntoView();var r=v.current.nextSibling.querySelector(".woocommerce-table__table"),r=$.focus.focusable.find(r);r.length&&r[0].focus()},rows:B,rowsPerPage:parseInt(h.per_page,10)||M.QUERY_DEFAULTS.pageSize,summary:D,totalRows:T,...p}))};i.propTypes={baseSearchQuery:r().object,compareBy:r().string,compareParam:r().string,columnPrefsKey:r().string,endpoint:r().string,getHeadersContent:r().func.isRequired,getRowsContent:r().func.isRequired,getSummary:r().func,itemIdField:r().string,labels:r().shape({compareButton:r().string,downloadButton:r().string,helpText:r().string,placeholder:r().string}),primaryData:r().object,searchBy:r().string,summaryFields:r().arrayOf(r().string),tableData:r().object.isRequired,tableQuery:r().object,title:r().string.isRequired},i.defaultProps={primaryData:{},tableData:{items:{data:[],totalResults:0},query:{}},tableQuery:{},compareParam:"filter",downloadable:!1,onSearch:Y.noop,baseSearchQuery:{}};const m={},p=(0,e.compose)((0,t.withSelect)((e,t)=>{var{endpoint:t,getSummary:r,isRequesting:a,query:o,tableData:s,tableQuery:n,filters:l,advancedFilters:i,summaryFields:c,extendedItemsStoreName:d}=t,u=e(M.SETTINGS_STORE_NAME).getSetting("wc_admin","wcAdminSettings")["woocommerce_default_date_range"];return a?m:(a=e(M.REPORTS_STORE_NAME),d&&e(d),{primaryData:r?(0,M.getReportChartData)({endpoint:t,dataType:"primary",query:o,select:e,selector:a,filters:l,advancedFilters:i,defaultDateRange:u,fields:c}):m,tableData:s||(0,M.getReportTableData)({endpoint:t,query:o,select:e,selector:a,tableQuery:n,filters:l,advancedFilters:i,defaultDateRange:u}),query:o})}),(0,t.withDispatch)(e=>{var t=e(M.EXPORT_STORE_NAME)["startExport"],e=e("core/notices")["createNotice"];return{createNotice:e,startExport:t}}))(i);e=(0,d.getSetting)("admin",{});const T="object"==typeof e&&1!==e.length&&e.stockStatuses?e.stockStatuses:(0,d.getSetting)("stockStatuses",{});class b extends Q.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,V.__)("Bundle Title","woocommerce-product-bundles"),key:"product_name",required:!0,isLeftAligned:!0,isSortable:!0},{label:(0,V.__)("SKU","woocommerce-product-bundles"),key:"sku",hiddenByDefault:!0,isSortable:!0},{label:(0,V.__)("Bundles Sold","woocommerce-product-bundles"),key:"items_sold",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0},{label:(0,V.__)("Bundled Items Sold","woocommerce-product-bundles"),key:"bundled_items_sold",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0},{label:(0,V.__)("Net Sales","woocommerce-product-bundles"),screenReaderLabel:(0,V.__)("Net Sales","woocommerce-product-bundles"),key:"net_revenue",required:!0,isSortable:!0,isNumeric:!0},{label:(0,V.__)("Orders","woocommerce-product-bundles"),key:"orders_count",isSortable:!0,isNumeric:!0},{label:(0,V.__)("Status","woocommerce-product-bundles"),key:"stock_status"}].filter(Boolean)}getRowsContent(e=[]){var t=this.props["query"];const d=(0,H.getPersistedQuery)(t),{render:u,formatDecimal:m,getCurrencyConfig:r}=this.context,p=r();return(0,Y.map)(e,e=>{var{product_id:t,items_sold:r,bundled_items_sold:a,net_revenue:o,orders_count:s}=e,e=e.extended_info||{},{sku:n,stock_status:l}=e,e=(0,g.decodeEntities)(e.name),i=(0,H.getNewPath)(d,"/analytics/orders",{filter:"advanced",product_includes:t}),t=(0,H.getNewPath)(d,"/analytics/products",{filter:"single_product",products:t}),c="insufficientstock"===l?(0,V.__)("Insufficient Stock","woocommerce-product-bundles"):T[l];return[{display:(0,A.createElement)(j.Link,{href:t,type:"wc-admin"},e),value:e},{display:n,value:n},{display:(0,y.formatValue)(p,"number",r),value:r},{display:(0,y.formatValue)(p,"number",a),value:a},{display:u(o),value:m(o)},{display:(0,A.createElement)(j.Link,{href:i,type:"wc-admin"},s),value:s},{display:c,value:l}].filter(Boolean)})}getSummary(e){var{products_count:e=0,items_sold:t=0,bundled_items_sold:r=0,net_revenue:a=0,orders_count:o=0}=e,{formatAmount:s,getCurrencyConfig:n}=this.context,n=n();return[{label:(0,V._n)("bundle","bundles",e,"woocommerce-product-bundles"),value:(0,y.formatValue)(n,"number",e)},{label:(0,V._n)("sale","sales",t,"woocommerce-product-bundles"),value:(0,y.formatValue)(n,"number",t)},{label:(0,V._n)("bundled item sold","bundled items sold",r,"woocommerce-product-bundles"),value:(0,y.formatValue)(n,"number",r)},{label:(0,V.__)("net sales","woocommerce-product-bundles"),value:s(a)},{label:(0,V._n)("order","orders",o,"woocommerce-product-bundles"),value:(0,y.formatValue)(n,"number",o)}]}render(){var{filters:e,isRequesting:t,hideCompare:r,query:a}=this.props;return(0,A.createElement)(p,{endpoint:"bundles",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["products_count","items_sold","bundled_items_sold","net_revenue","orders_count"],itemIdField:"product_id",isRequesting:t,query:a,compareBy:r?void 0:"bundles",tableQuery:{orderby:a.orderby||"items_sold",order:a.order||"desc",extended_info:!0,segmentby:a.segmentby},title:(0,V.__)("Bundles","woocommerce-product-bundles"),columnPrefsKey:"bundles_report_columns",filters:e})}}b.contextType=a;const x=b;t=(0,d.getSetting)("admin",{});const h="object"==typeof t&&1!==t.length&&t.stockStatuses?t.stockStatuses:(0,d.getSetting)("stockStatuses",{});class w extends Q.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this)}getHeadersContent(){return[{label:(0,V.__)("Bundle","woocommerce-product-bundles"),key:"bundle_name",required:!0,defaultSort:!0,isSortable:!0,isLeftAligned:!0},{label:(0,V.__)("Bundled Product","woocommerce-product-bundles"),key:"product_name",isSortable:!0},{label:(0,V.__)("Stock Status","woocommerce-product-bundles"),key:"stock_status"},{label:(0,V.__)("Remaining Stock","woocommerce-product-bundles"),key:"stock_quantity",isSortable:!1},{label:(0,V.__)("Required Stock","woocommerce-product-bundles"),key:"units_required",isSortable:!0,required:!0,isNumeric:!0}].filter(Boolean)}getRowsContent(e=[]){var{}=this.props,t=this.context["getCurrencyConfig"];const c=t();return(0,Y.map)(e,e=>{var{bundle_id:t,product_id:r}=e,{name:e,manage_stock:a,stock_quantity:o,bundle_name:s,units_required:n,stock_status:l}=e.extended_info||{},s=(0,g.decodeEntities)(s),t=(0,d.getAdminLink)("post.php?post="+t+"&action=edit"),e=(0,g.decodeEntities)(e),r=(0,d.getAdminLink)("post.php?post="+r+"&action=edit"),i=h[l];return[{display:(0,A.createElement)(j.Link,{href:t,type:"wp-admin"},s),value:s},{display:(0,A.createElement)(j.Link,{href:r,type:"wp-admin"},e),value:e},{display:i,value:h[l]},{display:a?(0,y.formatValue)(this.context.getCurrencyConfig(),"number",o):(0,V.__)("N/A","woocommerce-product-bundles"),value:o},{display:(0,y.formatValue)(c,"number",n),value:n}].filter(Boolean)})}render(){var{filters:e,isRequesting:t,hideCompare:r,query:a}=this.props;return(0,A.createElement)(p,{endpoint:"bundles/stock",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,itemIdField:"product_id",isRequesting:t,query:a,compareBy:r?void 0:"bundles",tableQuery:{orderby:a.orderby||"product_name",order:a.order||"asc",extended_info:!0,segmentby:a.segmentby},title:(0,V.__)("Stock","woocommerce-product-bundles"),columnPrefsKey:"bundles_stock_report_columns",filters:e})}}w.contextType=a;const F=w;function P(e=""){var t=f;return(e=(0,H.getIdsFromQuery)(e)).length<1?Promise.resolve([]):(e={include:e.join(","),per_page:e.length},k()({path:(0,D.addQueryArgs)(t,e)}).then(e=>e.map(v)))}var f,v;f=M.NAMESPACE+"/products",v=e=>({key:e.id,label:e.name});const C=(0,I.applyFilters)("woocommerce_admin_bundles_report_charts",[{key:"items_sold",label:(0,V.__)("Bundles Sold","woocommerce-product-bundles"),order:"desc",orderby:"items_sold",type:"number"},{key:"bundled_items_sold",label:(0,V.__)("Bundled Items Sold","woocommerce-product-bundles"),order:"desc",orderby:"bundled_items_sold",type:"number"},{key:"net_revenue",label:(0,V.__)("Net Sales","woocommerce-product-bundles"),order:"desc",orderby:"net_revenue",type:"currency"},{key:"orders_count",label:(0,V.__)("Orders","woocommerce-product-bundles"),order:"desc",orderby:"orders_count",type:"number"}]),R=[{label:(0,V.__)("View","woocommerce-product-bundles"),staticParams:["filter","products"],param:"section",showFilters:()=>!0,filters:[{label:(0,V.__)("Revenue","woocommerce-product-bundles"),value:"all"},{label:(0,V.__)("Stock","woocommerce-product-bundles"),value:"stock"}]},{label:(0,V.__)("Show","woocommerce-product-bundles"),staticParams:["section","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,V.__)("All Bundles","woocommerce-product-bundles"),value:"all"},{label:(0,V.__)("Single Bundle","woocommerce-product-bundles"),value:"select_bundle",subFilters:[{component:"Search",value:"single_product",path:["select_bundle"],settings:{type:"products",param:"products",getLabels:P,labels:{placeholder:(0,V.__)("Type to search for a bundle","woocommerce-product-bundles"),button:(0,V.__)("Single Bundle","woocommerce-product-bundles")}}}]}]}];(0,d.getSetting)("manageStock","no");class S extends Q.Component{getChartMeta(){var{}=this.props;return{itemsLabel:(0,V.__)("%d bundles","woocommerce-product-bundles"),mode:"time-comparison"}}render(){var e,{itemsLabel:t,mode:r}=this.getChartMeta(),{path:a,query:o,isError:s,isRequesting:n}=this.props;return s?(0,A.createElement)(ReportError,{isError:!0}):(s={...o},e="stock"!==o.section,r="stock"!==o.section?(0,A.createElement)(Q.Fragment,null,(0,A.createElement)(q,{mode:r,charts:C,endpoint:"bundles",isRequesting:n,query:s,selectedChart:u(o.chart,C),filters:R}),(0,A.createElement)(E,{charts:C,mode:r,endpoint:"bundles",isRequesting:n,itemsLabel:t,path:a,query:s,selectedChart:u(s.chart,C),filters:R}),(0,A.createElement)(x,{isRequesting:n,hideCompare:!0,query:o,filters:R})):(0,A.createElement)(Q.Fragment,null,(0,A.createElement)("p",null,(0,Q.createInterpolateElement)((0,V.__)("Use this report to identify Bundles with <strong>Insufficient Stock</strong>, and re-stock their contents.","woocommerce-product-bundles"),{strong:(0,A.createElement)("strong",null)})),(0,A.createElement)(F,{isRequesting:n,hideCompare:!0,query:o,filters:R})),(0,A.createElement)(Q.Fragment,null,(0,A.createElement)(j.ReportFilters,{query:o,path:a,showDatePicker:!1,filters:R}),(0,A.createElement)(j.ReportFilters,{query:o,path:a,showDatePicker:e}),r))}}S.propTypes={path:r().string.isRequired,query:r().object.isRequired};const N=S;(0,I.addFilter)("woocommerce_admin_reports_list","woocommerce-product-bundles",e=>[...e,{report:"bundles",title:(0,V._x)("Bundles","analytics report table","woocommerce-product-bundles"),component:N,navArgs:{id:"wc-pb-bundles-analytics-report"}}])})()})();