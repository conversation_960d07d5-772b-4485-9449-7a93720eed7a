(()=>{"use strict";var e=window.wp.hooks;const n=window.wp.i18n,t=window.wp.element;(0,e.addFilter)("experimental_woocommerce_admin_product_tour_steps","woocommerce-product-bundles",(e,o)=>{return"product-bundles"!==o?e:[{referenceElements:{desktop:"._regular_price_field"},focusElement:{desktop:"#_regular_price"},meta:{name:"product-bundle-price",heading:(0,n.__)("Assign a base price to your Bundle","woocommerce-product-bundles"),descriptions:{desktop:(0,t.createInterpolateElement)((0,n.__)("Use these fields to define a base price for your Bundle. This can be handy if your Bundle does not include any options that affect its price. If you prefer to <link>preserve the prices of individual bundled items</link>, you may omit this step.","woocommerce-product-bundles"),{link:(0,t.createElement)("a",{href:"https://woocommerce.com/document/bundles/bundles-configuration/#pricing","aria-label":(0,n.__)("Product Bundles configuration documentation.","woocommerce-product-bundles"),target:"_blank"})})}}},{referenceElements:{desktop:".product_data .bundled_products_tab"},meta:{name:"add-bundled-products",heading:(0,n.__)("Add bundled items","woocommerce-product-bundles"),descriptions:{desktop:(0,t.createInterpolateElement)((0,n.__)("You can add both Simple and Variable products to this Bundle. Once added, every bundled item reveals <link>additional pricing, shipping and display options</link> to configure.","woocommerce-product-bundles"),{link:(0,t.createElement)("a",{href:"https://woocommerce.com/document/bundles/bundles-configuration/#bundled-product-options","aria-label":(0,n.__)("Product Bundles configuration documentation.","woocommerce-product-bundles"),target:"_blank"})})}}},{referenceElements:{desktop:".product_data .shipping_tab"},meta:{name:"shipping-options",heading:(0,n.__)("Configure shipping options","woocommerce-product-bundles"),descriptions:{desktop:(0,n.__)("Assembled Bundles have their own dimensions and weight: Choose the Assembled option if the items contained in your Bundle are physically assembled in a common container. Unassembled Bundles do not have any shipping options to configure: Choose the Unassembled option if the items of your Bundle are shipped in their existing packaging.","woocommerce-product-bundles")}}}]})})();