!function(l){function i(_){void 0!==_.price_data.size_min&&void 0!==_.price_data.size_max&&(_.min_max_validation={min:_.price_data.size_min,max:_.price_data.size_max,bind_validation_handler:function(){var o=this;_.$bundle_data.on("woocommerce-product-bundle-validate",function(_,a){var i=0,n="",e="",m=!0;if(l.each(a.bundled_items,function(_,a){a.is_selected()&&(i+=a.get_quantity())}),""!==o.min&&i<parseInt(o.min)?(m=!1,e=1===o.min?o.min===o.max?wc_pb_min_max_items_params.i18n_min_zero_max_qty_error_singular:wc_pb_min_max_items_params.i18n_min_qty_error_singular:(e=o.min===o.max?wc_pb_min_max_items_params.i18n_min_max_qty_error_plural:wc_pb_min_max_items_params.i18n_min_qty_error_plural).replace("%q",parseInt(o.min))):""!==o.max&&i>parseInt(o.max)&&(m=!1,e=1===o.max?o.min===o.max?wc_pb_min_max_items_params.i18n_min_max_qty_error_singular:wc_pb_min_max_items_params.i18n_max_qty_error_singular:(e=o.min===o.max?wc_pb_min_max_items_params.i18n_min_max_qty_error_plural:wc_pb_min_max_items_params.i18n_max_qty_error_plural).replace("%q",parseInt(o.max))),!m){if(0===i){if(n="","no"===a.price_data.zero_items_allowed){for(var t=a.get_validation_messages(),s=[],r=0;r<=t.length-1;r++)t[r]!==wc_bundle_params.i18n_zero_qty_error&&s.push(t[r]);a.validation_messages=s}}else n=1===i?wc_pb_min_max_items_params.i18n_qty_error_singular:wc_pb_min_max_items_params.i18n_qty_error_plural;n=n.replace("%s",i),0<a.validation_messages.length||""===n?a.add_validation_message(e):a.add_validation_message('<span class="status_msg"><span class="bundled_items_selection_msg">'+e+'</span><span class="bundled_items_selection_status">'+n+"</span></span>")}})}},_.min_max_validation.bind_validation_handler())}l("body .component").on("wc-composite-component-loaded",function(_,a){"bundle"===a.get_selected_product_type()&&(a=a.get_bundle_script())&&(i(a),a.update_bundle_task())}),l(".bundle_form .bundle_data").each(function(){l(this).on("woocommerce-product-bundle-initializing",function(_,a){a.is_composited()||i(a)})})}(jQuery,(window,document));