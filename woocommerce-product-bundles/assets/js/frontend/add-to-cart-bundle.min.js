var wc_pb_bundle_scripts={};function wc_pb_price_format(t,e){return e=void 0!==e&&e,wc_pb_woocommerce_number_format(wc_pb_number_format(t),e)}function wc_pb_woocommerce_number_format(t,e){var i=wc_bundle_params.currency_format_decimal_sep,_=wc_bundle_params.currency_position,a=wc_bundle_params.currency_symbol,n=wc_bundle_params.currency_format_trim_zeros,s=wc_bundle_params.currency_format_num_decimals;if(e=void 0!==e&&e,"yes"==n&&0<s){for(var d=0;d<s;d++)i+="0";t=t.replace(i,"")}n=String(t),t=e?a:'<span class="woocommerce-Price-currencySymbol">'+a+"</span>";return"left"===_?n=t+n:"right"===_?n+=t:"left_space"===_?n=t+" "+n:"right_space"===_&&(n=n+" "+t),n=e?n:'<span class="woocommerce-Price-amount amount">'+n+"</span>"}function wc_pb_number_format(t){var e=wc_bundle_params.currency_format_num_decimals,i=wc_bundle_params.currency_format_decimal_sep,_=wc_bundle_params.currency_format_thousand_sep,e=isNaN(e=Math.abs(e))?2:e,i=null==i?",":i,_=null==_?".":_,a=t<0?"-":"",n=parseInt(t=Math.abs(+t||0).toFixed(e),10)+"",s=3<(s=n.length)?s%3:0;return a+(s?n.substring(0,s)+_:"")+n.substring(s).replace(/(\d{3})(?=\d)/g,"$1"+_)+(e?i+Math.abs(t-n).toFixed(e).slice(2):"")}function wc_pb_number_round(t,e){e=void 0===e?wc_bundle_params.currency_format_num_decimals:parseInt(e,10),e=Math.pow(10,e);return Math.round(t*e)/e}function wc_pb_format_list(t,e){var i="",_=t.length,a=e&&e.plain,n=e&&e.plain_sep;if(0<_)for(var s,d=0,r=0;r<_;r++)d++,s=a?t[r]:wc_bundle_params.i18n_string_list_item.replace("%s",t[r]),i=1==_||1==d?s:(d!==_||n?wc_bundle_params.i18n_string_list_sep:wc_bundle_params.i18n_string_list_last_sep).replace("%s",i).replace("%v",s);return i}jQuery.fn.wc_get_bundle_script=function(){var t=jQuery(this);return!!t.hasClass("bundle_form")&&(t=t.data("script_id"),void 0!==wc_pb_bundle_scripts[t])&&wc_pb_bundle_scripts[t]},function(b){function o(t){var m=this;this.bundle_id=t.bundle_id,this.$bundle_form=t.$bundle_form,this.$bundle_data=t.$bundle_data,this.$bundle_wrap=t.$bundle_data.find(".bundle_wrap"),this.$bundled_items=t.$bundle_form.find(".bundled_product"),this.$bundle_availability=t.$bundle_data.find(".bundle_availability"),this.$bundle_price=t.$bundle_data.find(".bundle_price"),this.$bundle_button=t.$bundle_data.find(".bundle_button"),this.$bundle_error=t.$bundle_data.find(".bundle_error"),this.$bundle_error_content=this.$bundle_error.find("ul.msg"),this.$bundle_quantity=this.$bundle_button.find("input.qty"),this.$nyp=this.$bundle_data.find(".nyp"),this.$addons_totals=this.$bundle_data.find("#product-addons-total"),this.show_addons_totals=!1,this.bundled_items={},this.price_data=t.$bundle_data.data("bundle_form_data"),this.$initial_stock_status=!1,this.update_bundle_timer=!1,this.update_price_timer=!1,this.validation_messages=[],this.is_initialized=!1,this.composite_data=t.composite_data,this.dirty_subtotals=!1,this.filters=!1,this.api={get_bundle_totals:function(){return m.price_data.totals},get_bundled_item_totals:function(t){return m.price_data["bundled_item_"+t+"_totals"]},get_bundled_item_recurring_totals:function(t){return m.price_data["bundled_item_"+t+"_recurring_totals"]},get_bundle_validation_status:function(){return m.passes_validation()?"pass":"fail"},get_bundle_validation_messages:function(){return m.get_validation_messages()},get_bundle_stock_status:function(){return 0<m.$bundle_wrap.find("p.out-of-stock").not(".inactive").length?"out-of-stock":"in-stock"},get_bundle_availability:function(){var t=m.$bundle_wrap.find("p.stock");return t=t.hasClass("inactive")?!1!==m.$initial_stock_status?m.$initial_stock_status.clone().wrap("<div></div>").parent().html():"":t.clone().removeAttr("style").wrap("<div></div>").parent().html()},get_bundle_configuration:function(){var _={};return 0!==m.bundled_items.length&&(b.each(m.bundled_items,function(t,e){var i={title:e.get_title(),product_title:e.get_product_title(),product_id:e.get_product_id(),variation_id:e.get_variation_id(),quantity:m.price_data.quantities[e.bundled_item_id],product_type:e.get_product_type()};_[e.bundled_item_id]=i}),_)}},this.initialize=function(){this.filters=new i,this.has_addons()?(1==this.$addons_totals.data("show-sub-total")||this.is_composited()&&this.composite_data.component.show_addons_totals)&&(this.$addons_totals.data("show-sub-total",0),this.$bundle_price.after(this.$addons_totals),this.show_addons_totals=!0,m.$bundle_data.trigger("woocommerce-product-addons-update")):this.$addons_totals=!1,0<this.$bundle_wrap.find("p.stock").length&&(this.$initial_stock_status=this.$bundle_wrap.find("p.stock").clone()),this.price_data?this.$bundle_data.data("bundle_price_data")||this.$bundle_data.data("bundle_price_data",this.price_data):this.price_data=t.$bundle_data.data("bundle_price_data"),this.price_data.suffix_exists=""!==wc_bundle_params.price_display_suffix,this.price_data.suffix=""!==wc_bundle_params.price_display_suffix?' <small class="woocommerce-price-suffix">'+wc_bundle_params.price_display_suffix+"</small>":"",this.price_data.suffix_contains_price_incl=-1<wc_bundle_params.price_display_suffix.indexOf("{price_including_tax}"),this.price_data.suffix_contains_price_excl=-1<wc_bundle_params.price_display_suffix.indexOf("{price_excluding_tax}"),this.$bundle_button.find('input[name*="bundle_variation"], input[name*="bundle_attribute"]').remove(),this.bind_event_handlers(),this.viewport_resized(),this.init_bundled_items(),this.is_composited()&&this.init_composite(),"yes"===wc_bundle_params.is_pao_installed&&void 0!==window.WC_PAO&&this.match_bundled_items_addons_forms(),this.$bundle_data.trigger("woocommerce-product-bundle-initializing",[this]),b.each(this.bundled_items,function(t,e){e.init_scripts()}),this.update_bundle_task(),this.is_initialized=!0,this.$bundle_form.addClass("initialized"),this.$bundle_data.trigger("woocommerce-product-bundle-initialized",[this])},this.shutdown=function(){this.$bundle_form.find("*").off(),!1!==this.composite_data&&this.remove_composite_hooks()},this.init_composite=function(){this.composite_data.composite.actions.add_action("component_scripts_initialized_"+this.composite_data.component.step_id,this.component_scripts_initialized_action,10,this)},this.component_scripts_initialized_action=function(){(void 0!==this.composite_data.component.component_selection_model.selected_product?parseInt(this.composite_data.component.component_selection_model.selected_product,10)===parseInt(this.bundle_id,10):parseInt(this.composite_data.component.component_selection_model.get("selected_product"),10)===parseInt(this.bundle_id,10))?this.add_composite_hooks():this.remove_composite_hooks()},this.add_composite_hooks=function(){this.composite_data.composite.filters.add_filter("component_is_valid",this.cp_component_is_valid_filter,10,this),this.composite_data.composite.filters.add_filter("component_selection_formatted_title",this.cp_component_selection_formatted_title_filter,10,this),this.composite_data.composite.filters.add_filter("component_selection_meta",this.cp_component_selection_meta_filter,10,this),this.composite_data.composite.filters.add_filter("component_totals",this.cp_component_totals_filter,10,this),this.composite_data.composite.filters.add_filter("component_configuration",this.cp_component_configuration_filter,10,this),this.composite_data.composite.actions.add_action("validate_step",this.cp_validation_messages_action,10,this)},this.remove_composite_hooks=function(){this.composite_data.composite.filters.remove_filter("component_is_valid",this.cp_component_is_valid_filter),this.composite_data.composite.filters.remove_filter("component_selection_formatted_title",this.cp_component_selection_formatted_title_filter),this.composite_data.composite.filters.remove_filter("component_selection_meta",this.cp_component_selection_meta_filter),this.composite_data.composite.filters.remove_filter("component_totals",this.cp_component_totals_filter),this.composite_data.composite.filters.remove_filter("component_configuration",this.cp_component_configuration_filter),this.composite_data.composite.actions.remove_action("component_scripts_initialized_"+this.composite_data.component.step_id,this.component_scripts_initialized_action),this.composite_data.composite.actions.remove_action("validate_step",this.cp_validation_messages_action)},this.cp_component_configuration_filter=function(t,e){return e.step_id===this.composite_data.component.step_id&&parseInt(e.get_selected_product(),10)===parseInt(m.bundle_id,10)&&(t.bundled_items=m.api.get_bundle_configuration()),t},this.cp_component_totals_filter=function(t,e,i){var _,a;return e.step_id===this.composite_data.component.step_id&&parseInt(e.get_selected_product(),10)===parseInt(m.bundle_id,10)?(_=b.extend(!0,{},m.price_data),a=m.has_addons()?m.get_addons_raw_price():0,i=void 0===i?e.get_selected_quantity():i,0<a&&(_.base_price=Number(_.base_price)+Number(a)),_=m.calculate_subtotals(!1,_,i),(_=m.calculate_totals(_)).totals):t},this.cp_component_selection_formatted_title_filter=function(t,e,i,_,a){var n;return a.step_id===this.composite_data.component.step_id&&parseInt(a.get_selected_product(),10)===parseInt(this.bundle_id,10)&&(n=0,b.each(this.bundled_items,function(t,e){0<e.$bundled_item_cart.data("quantity")&&n++}),this.group_mode_supports("component_multiselect"))&&(0===n?t=wc_composite_params.i18n_no_selection:(a=this.cp_get_formatted_contents(a))&&(t=a)),t},this.cp_component_selection_meta_filter=function(t,e){var i;return e.step_id===this.composite_data.component.step_id&&parseInt(e.get_selected_product(),10)===parseInt(this.bundle_id,10)&&(i=0,b.each(this.bundled_items,function(t,e){0<e.$bundled_item_cart.data("quantity")&&i++}),0!==i)&&!1===this.group_mode_supports("component_multiselect")&&""!==(e=this.cp_get_formatted_contents(e))&&t.push({meta_key:wc_bundle_params.i18n_contents,meta_value:e}),t},this.cp_get_formatted_contents=function(t){var i="",s=[],d=t.get_selected_quantity();return b.each(this.bundled_items,function(t,e){if(e.$self.hasClass("bundled_item_hidden"))return!0;var i,_,a,n;0<e.$bundled_item_cart.data("quantity")&&(i=0<(i=e.$bundled_item_image.find("img").first()).length&&i.get(0).outerHTML,a=parseInt(e.$bundled_item_cart.data("quantity")*d,10),_=wc_cp_get_variation_data(e.$bundled_item_cart.find(".variations")),e=e.$bundled_item_cart.data("title"),a=1<a?"<strong>"+wc_composite_params.i18n_qty_string.replace("%s",a)+"</strong>":"",n="",0<_.length&&(b.each(_,function(t,e){n=n+'<span class="bundled_meta_element"><span class="bundled_meta_key">'+e.meta_key+':</span> <span class="bundled_meta_value">'+e.meta_value+"</span>",t!==_.length-1&&(n+='<span class="bundled_meta_value_sep">, </span>'),n+="</span>"}),e=wc_bundle_params.i18n_title_meta_string.replace("%t",e).replace("%m",'<span class="content_bundled_product_meta">'+n+"</span>")),e=wc_composite_params.i18n_title_string.replace("%t",e).replace("%q",a).replace("%p",""),s.push({title:e,image:i}))}),0<s.length&&(i+='<span class="content_bundled_product_details_wrapper">',b.each(s,function(t,e){i=i+'<span class="content_bundled_product_details">'+(e.image?'<span class="content_bundled_product_image">'+e.image+"</span>":"")+'<span class="content_bundled_product_title">'+e.title+"</span></span>"}),i+="</span>"),i},this.cp_component_is_valid_filter=function(t,e,i){return t=i.step_id===this.composite_data.component.step_id&&parseInt(i.get_selected_product(e),10)===parseInt(this.bundle_id,10)&&0<i.get_selected_quantity()&&i.is_visible()?this.passes_validation():t},this.cp_validation_messages_action=function(i,t){i.step_id===this.composite_data.component.step_id&&!1===t&&parseInt(i.get_selected_product(),10)===parseInt(this.bundle_id,10)&&(t=this.get_validation_messages(),b.each(t,function(t,e){i.add_validation_message(e),i.add_validation_message(e,"composite")}))},this.get_ajax_url=function(t){return woocommerce_params.wc_ajax_url.toString().replace("%%endpoint%%",t)},this.viewport_resized=function(){this.is_composited()||(this.$bundle_form.width()<=wc_bundle_params.responsive_breakpoint?this.$bundle_form.addClass("small_width"):this.$bundle_form.removeClass("small_width"))},this.bind_event_handlers=function(){b(window).on("resize",function(){clearTimeout(m.viewport_resize_timer),m.viewport_resize_timer=setTimeout(function(){m.viewport_resized()},50)}),m.has_addons()&&m.$bundle_data.on("updated_addons",m.updated_addons_handler),m.is_composited()&&m.$bundle_quantity.on("input change",function(){m.$bundle_data.trigger("woocommerce-product-bundle-update")}),this.$bundle_data.on("woocommerce-nyp-updated-item",function(t){m.$nyp.is(":visible")&&(m.price_data.base_regular_price=m.$nyp.data("price"),m.price_data.base_price=m.price_data.base_regular_price,m.is_initialized)&&(m.dirty_subtotals=!0,m.update_totals()),t.stopPropagation()}).on("woocommerce-product-bundle-validation-status-changed",function(t,e){e.updated_totals()}).on("click",".bundle_add_to_cart_button",function(t){b(this).hasClass("disabled")&&(t.preventDefault(),window.alert(wc_bundle_params.i18n_validation_alert))}).on("woocommerce-product-bundle-update-totals",function(t,e,i){i=void 0===i?m:i;(e=void 0!==e&&e)&&(i.dirty_subtotals=!0),i.update_totals()}).on("woocommerce-bundled-item-totals-changed",function(t,e){e.has_addons()&&e.render_addons_totals()}).on("woocommerce-product-bundle-update",function(t,e){var i=void 0===e?m:e.get_bundle();e?i.update_bundle(e):i.update_bundle()})},this.init_bundled_items=function(){m.$bundled_items.each(function(t){m.bundled_items[t]=new e(m,b(this),t),m.bind_bundled_item_event_handlers(m.bundled_items[t])})},this.match_bundled_items_addons_forms=function(){var i=window.WC_PAO.initialized_forms;m.$bundled_items.each(function(t){var e=m.bundled_items[t].$bundled_item_cart;b.each(i,function(){if(this.$el[0]===e[0])return m.bundled_items[t].addons_form=this,!1})})},this.bind_bundled_item_event_handlers=function(n){n.$self.on("input change","input.bundled_qty",function(t){var e=b(this),i=parseFloat(e.val()),_=parseFloat(e.attr("min")),a=parseFloat(e.attr("max"));"yes"===wc_bundle_params.force_min_max_qty_input&&"change"===t.type&&(0<=_&&(i<_||isNaN(i))&&(i=_),e.val(i=0<a&&a<i?a:i)),n.is_nyp()&&!n.is_optional()&&0===_&&n.$nyp.data("optional_status",0<i),n.update_selection_title(),m.$bundle_data.trigger("woocommerce-product-bundle-update",[n])}).on("change",".bundled_product_optional_checkbox input",function(t){var e;b(this).is(":checked")?(n.$bundled_item_content.css({height:"",display:"block",position:"absolute"}),void 0===(e=n.$bundled_item_content.get(0).getBoundingClientRect().height)&&(e=n.$bundled_item_content.outerHeight()),n.$bundled_item_content.css({height:"",position:"",display:"none"}),e&&(n.$bundled_item_content.addClass("bundled_item_cart_content--populated"),n.$bundled_item_content.slideDown(200)),n.set_selected(!0),n.$self.find(".bundled_item_qty_col .quantity").removeClass("quantity_hidden"),n.is_nyp()&&n.$nyp.trigger("wc-nyp-update",[{force:!0}]),n.$bundled_item_cart.find(".variations select:eq(0)").trigger("change")):(n.$bundled_item_content.slideUp(200),n.set_selected(!1),n.$self.find(".bundled_item_qty_col .quantity").addClass("quantity_hidden"),n.reset_variation_image()&&(n.maybe_add_wc_core_gallery_class(),n.$bundled_item_cart.trigger("reset_image"),n.maybe_remove_wc_core_gallery_class())),n.update_selection_title(),m.$bundle_data.trigger("woocommerce-product-bundle-update",[n]),t.stopPropagation()}).on("found_variation",function(t,e){n.set_variation_id(e.variation_id);var i=e.price,_=e.regular_price;n.is_nyp()&&e.is_nyp&&n.$nyp.is(":visible")&&(i=_=n.$nyp.data("price")),m.price_data.prices[n.bundled_item_id]=Number(i),m.price_data.regular_prices[n.bundled_item_id]=Number(_),m.price_data.prices_tax[n.bundled_item_id]=e.price_tax,m.price_data.recurring_prices[n.bundled_item_id]=Number(e.recurring_price),m.price_data.regular_recurring_prices[n.bundled_item_id]=Number(e.regular_recurring_price),m.price_data.recurring_html[n.bundled_item_id]=e.recurring_html,m.price_data.recurring_keys[n.bundled_item_id]=e.recurring_key,m.price_data.quantities_available[n.bundled_item_id]=e.avail_qty,m.price_data.is_in_stock[n.bundled_item_id]=e.is_in_stock?"yes":"no",m.price_data.backorders_allowed[n.bundled_item_id]=e.backorders_allowed?"yes":"no",m.price_data.backorders_require_notification[n.bundled_item_id]=e.backorders_require_notification,n.maybe_remove_wc_core_gallery_class(),n.reset_variation_image()&&(n.maybe_add_wc_core_gallery_class(),n.$bundled_item_cart.trigger("reset_image"),n.maybe_remove_wc_core_gallery_class()),n.update_selection_title(),m.$bundle_data.trigger("woocommerce-product-bundle-update",[n]),t.stopPropagation()}).on("reset_image",function(){n.maybe_remove_wc_core_gallery_class()}).on("woocommerce-product-addons-update",function(t){t.stopPropagation()}).on("woocommerce_variation_select_focusin",function(t){t.stopPropagation()}).on("woocommerce_variation_has_changed",function(t){n.$reset_bundled_variations&&(n.variation_id?n.$reset_bundled_variations.slideDown(200):n.$reset_bundled_variations.slideUp(200)),t.stopPropagation()}).on("woocommerce_variation_select_change",function(t){n.set_variation_id(""),m.price_data.quantities_available[n.bundled_item_id]="",m.price_data.is_in_stock[n.bundled_item_id]="",m.price_data.backorders_allowed[n.bundled_item_id]="",m.price_data.backorders_require_notification[n.bundled_item_id]="",n.is_selected()&&n.maybe_add_wc_core_gallery_class(),n.$attribute_select&&n.$attribute_select.each(function(){if(""===b(this).val())return n.$bundled_item_cart.find(".bundled_item_wrap .stock").addClass("disabled"),m.$bundle_data.trigger("woocommerce-product-bundle-update",[n]),!1}),t.stopPropagation()}),n.has_addons()&&n.$bundled_item_cart.on("updated_addons",function(t){n.$addons_totals.html(n.addons_totals_html),m.$bundle_data.trigger("woocommerce-product-bundle-update",[n]),t.stopPropagation()}),n.is_nyp()&&n.$bundled_item_cart.on("woocommerce-nyp-updated-item",function(t){var e;n.$nyp.is(":visible")&&(e=n.$nyp.data("price"),m.price_data.prices[n.bundled_item_id]=e,m.price_data.regular_prices[n.bundled_item_id]=e,m.$bundle_data.trigger("woocommerce-product-bundle-update",[n])),t.stopPropagation()})},this.get_quantity=function(){var t=0<m.$bundle_quantity.length?m.$bundle_quantity.val():1;return isNaN(t)?1:parseInt(t,10)},this.get_bundled_items_availability=function(){var i=[],_=!0,a=[],n=!0;return b.each(m.bundled_items,function(t,e){if(e.has_insufficient_stock()&&(i.push(e.get_title(!0)),!e.is_visible()||!e.get_title(!0)))return _=!1}),0<i.length?_?wc_bundle_params.i18n_insufficient_stock_list.replace("%s",wc_pb_format_list(i,{plain:!0,plain_sep:!0})):wc_bundle_params.i18n_insufficient_stock_status:!m.$bundle_form.hasClass("bundle_out_of_stock")&&!m.$bundle_form.hasClass("bundle_insufficient_stock")&&(b.each(m.bundled_items,function(t,e){if(e.is_backordered()&&(a.push(e.get_title(!0)),!e.is_visible()||!e.get_title(!0)))return n=!1}),0<a.length?n?wc_bundle_params.i18n_on_backorder_list.replace("%s",wc_pb_format_list(a,{plain:!0,plain_sep:!0})):wc_bundle_params.i18n_on_backorder_status:"")},this.update_bundle=function(t){clearTimeout(m.update_bundle_timer),m.update_bundle_timer=setTimeout(function(){m.update_bundle_task(t)},5)},this.update_bundle_task=function(t){var e,i=!1,_=!1,a=!1===m.is_initialized?"":m.api.get_bundle_validation_status(),n=0,s=[],d=0,r=[],o=0,c=0,l=[];m.validation_messages=[],b.each(m.bundled_items,function(t,e){var i=e.is_selected()?e.get_quantity():0;o+=i,e.is_variable_product_type()&&""===e.get_variation_id()&&0<i&&(n++,e.is_visible())&&e.get_title(!0)&&s.push(e.get_title(!0)),e.is_selected()&&e.addons_form&&!e.addons_form.validation.validate()&&(e.has_pending_required_addons()?e.is_visible()&&e.get_title(!0)&&-1===b.inArray(e.get_title(!0),s)&&(n++,s.push(e.get_title(!0))):(d++,e.is_visible()&&e.get_title(!0)&&r.push(e.get_title(!0)))),e.is_nyp()&&!e.is_nyp_valid()&&(c++,e.is_visible())&&e.get_title(!0)&&l.push(e.get_title(!0))}),0<n&&(e="",e=n===s.length&&n<5?wc_bundle_params.i18n_validation_issues_for.replace("%c",wc_pb_format_list(s)).replace("%e",wc_bundle_params.i18n_select_options):wc_bundle_params.i18n_select_options,m.add_validation_message(e)),0<d&&(e="",e=d===r.length&&d<5?wc_bundle_params.i18n_validation_issues_for.replace("%c",wc_pb_format_list(r)).replace("%e",wc_bundle_params.i18n_review_product_addons):wc_bundle_params.i18n_select_options,m.add_validation_message(e)),0<c&&(e="",e=c===l.length&&c<5?wc_bundle_params.i18n_validation_issues_for.replace("%c",wc_pb_format_list(l)).replace("%e",wc_bundle_params.i18n_enter_valid_price_for):wc_bundle_params.i18n_enter_valid_price,m.add_validation_message(e)),0===o&&"no"===m.price_data.zero_items_allowed&&m.add_validation_message(wc_bundle_params.i18n_zero_qty_error),"yes"!==m.price_data.is_purchasable?m.add_validation_message(wc_bundle_params.i18n_unavailable_text):m.$bundle_data.trigger("woocommerce-product-bundle-validate",[m]),a!==m.api.get_bundle_validation_status()&&m.$bundle_data.trigger("woocommerce-product-bundle-validation-status-changed",[m]),"yes"===m.price_data.is_purchasable&&m.update_totals(t),b.each(m.bundled_items,function(t,e){e.has_insufficient_stock()&&(i=!0),e.is_backordered()&&e.backorders_require_notification()&&(_=!0)}),m.passes_validation()?(i?m.$bundle_button.find("button").addClass("disabled"):m.$bundle_button.find("button").removeClass("disabled"),setTimeout(function(){m.$bundle_error.slideUp(200)},1),m.$bundle_wrap.trigger("woocommerce-product-bundle-show")):m.hide_bundle(),(e=m.get_bundled_items_availability())?(m.$bundle_availability.html(e),m.$bundle_availability.slideDown(200)):m.$initial_stock_status?m.$bundle_availability.html(m.$initial_stock_status):(m.is_composited()&&m.$bundle_availability.find("p.stock").addClass("inactive"),m.$bundle_availability.slideUp(200)),m.is_composited()&&("function"==typeof m.composite_data.component.component_selection_model.set_stock_status&&(m.composite_data.component.component_selection_model.set_stock_status(i?"out-of-stock":"in-stock"),"function"==typeof m.composite_data.component.component_selection_model.set_availability)&&(_||this.has_backorder_notification()?m.composite_data.component.component_selection_model.set_availability(wc_bundle_params.i18n_available_on_backorder):m.composite_data.component.component_selection_model.set_availability("")),m.composite_data.composite.actions.do_action("component_selection_content_changed",[m.composite_data.component])),m.$bundle_data.trigger("woocommerce-product-bundle-updated",[m])},this.has_backorder_notification=function(){return"available-on-backorder"===m.price_data.availability_class},this.hide_bundle=function(t){var e,i=b("<ul/>");void 0===t?0<(e=m.get_validation_messages()).length?b.each(e,function(t,e){i.append(b("<li/>").html(e))}):i.append(b("<li/>").html(wc_bundle_params.i18n_unavailable_text)):i.append(b("<li/>").html(t.toString())),m.$bundle_error_content.html(i.html()),setTimeout(function(){m.$bundle_error.slideDown(200)},1),m.$bundle_button.find("button").addClass("disabled"),m.$bundle_wrap.trigger("woocommerce-product-bundle-hide")},this.update_price_data=function(){b.each(m.bundled_items,function(t,e){var i=e.$bundled_item_cart,_=e.bundled_item_id,a=e.get_quantity();m.price_data.quantities[_]=0,e.is_selected()&&0<a&&(m.price_data.quantities[_]=parseInt(a,10)),i.data("quantity",m.price_data.quantities[_]),e.is_variable_product_type()&&""===e.get_variation_id()&&(m.price_data.prices[_]=0,m.price_data.regular_prices[_]=0,m.price_data.recurring_prices[_]=0,m.price_data.regular_recurring_prices[_]=0,m.price_data.prices_tax[_]=!1),m.price_data.prices[_]=Number(m.price_data.prices[_]),m.price_data.regular_prices[_]=Number(m.price_data.regular_prices[_]),m.price_data.recurring_prices[_]=Number(m.price_data.recurring_prices[_]),m.price_data.regular_recurring_prices[_]=Number(m.price_data.regular_recurring_prices[_]),e.has_addons()&&e.update_addons_prices(),m.price_data.addons_prices[_]=Number(m.price_data.addons_prices[_]),m.price_data.regular_addons_prices[_]=Number(m.price_data.regular_addons_prices[_])})},this.update_totals=function(t){this.update_price_data(),this.calculate_subtotals(t),!m.dirty_subtotals&&!1!==m.is_initialized||(m.dirty_subtotals=!1,m.calculate_totals())},this.get_taxed_totals=function(t,e,i,_){_=void 0===_?1:_;var a=!(!i||void 0===i.incl)&&Number(i.incl),i=!(!i||void 0===i.excl)&&Number(i.excl),e={price:_*t,regular_price:_*e,price_incl_tax:_*t,price_excl_tax:_*t};return a&&i&&(e.price_incl_tax=wc_pb_number_round(e.price*a),e.price_excl_tax=wc_pb_number_round(e.price*i),"incl"===wc_bundle_params.tax_display_shop?(e.price=e.price_incl_tax,e.regular_price=wc_pb_number_round(e.regular_price*a)):(e.price=e.price_excl_tax,e.regular_price=wc_pb_number_round(e.regular_price*i))),e},this.calculate_subtotals=function(l,t,u){var e,i,_,p=void 0===t?m.price_data:t;if(u=void 0===u?1:parseInt(u,10),!1===(l=void 0!==l&&l)&&(e=Number(p.base_price),i=Number(p.base_regular_price),_=p.base_price_tax,p.base_price_totals=this.get_taxed_totals(e,i,_,u)),b.each(m.bundled_items,function(t,e){if(!1!==l&&l.bundled_item_id!==e.bundled_item_id)return!0;var i=e.is_sold_individually()&&0<p.quantities[e.bundled_item_id]?1:p.quantities[e.bundled_item_id]*u,_="variable"===e.get_product_type()?e.get_variation_id():e.get_product_id(),a=p.prices_tax[e.bundled_item_id],n=e.is_subscription()?p.regular_prices[e.bundled_item_id]:p.regular_prices[e.bundled_item_id]+p.regular_addons_prices[e.bundled_item_id],s=e.is_subscription()?p.prices[e.bundled_item_id]:p.prices[e.bundled_item_id]+p.addons_prices[e.bundled_item_id],d=p.regular_recurring_prices[e.bundled_item_id]+p.regular_addons_prices[e.bundled_item_id],r=p.recurring_prices[e.bundled_item_id]+p.addons_prices[e.bundled_item_id],o={price:0,regular_price:0,price_incl_tax:0,price_excl_tax:0},c={price:0,regular_price:0,price_incl_tax:0,price_excl_tax:0},_=("yes"===wc_bundle_params.calc_taxes?0<_&&0<i&&((0<s||0<n)&&(o=m.get_taxed_totals(s,n,a,i)),0<r||0<d)&&(c=m.get_taxed_totals(r,d,a,i)):(o.price=i*s,o.regular_price=i*n,o.price_incl_tax=i*s,o.price_excl_tax=i*s,c.price=i*r,c.regular_price=i*d,c.price_incl_tax=i*r,c.price_excl_tax=i*r),o=m.filters.apply_filters("bundled_item_totals",[o,e,u]),c=m.filters.apply_filters("bundled_item_recurring_totals",[c,e,u]),!1);m.totals_changed(p["bundled_item_"+e.bundled_item_id+"_totals"],o)&&(m.dirty_subtotals=_=!0,p["bundled_item_"+e.bundled_item_id+"_totals"]=o),m.totals_changed(p["bundled_item_"+e.bundled_item_id+"_recurring_totals"],c)&&(m.dirty_subtotals=_=!0,p["bundled_item_"+e.bundled_item_id+"_recurring_totals"]=c),_&&m.$bundle_data.trigger("woocommerce-bundled-item-totals-changed",[e])}),void 0!==t)return p},this.calculate_totals=function(t){void 0===t&&m.$bundle_data.trigger("woocommerce-product-bundle-calculate-totals",[m]);var e,a=void 0===t?m.price_data:t,i=!1,_={price:wc_pb_number_round(a.base_price_totals.price),regular_price:wc_pb_number_round(a.base_price_totals.regular_price),price_incl_tax:wc_pb_number_round(a.base_price_totals.price_incl_tax),price_excl_tax:wc_pb_number_round(a.base_price_totals.price_excl_tax)},n=(b.each(m.bundled_items,function(t,e){if(e.is_unavailable())return!0;e=a["bundled_item_"+e.bundled_item_id+"_totals"];void 0!==e&&(_.price+=wc_pb_number_round(e.price),_.regular_price+=wc_pb_number_round(e.regular_price),_.price_incl_tax+=wc_pb_number_round(e.price_incl_tax),_.price_excl_tax+=wc_pb_number_round(e.price_excl_tax))}),m.get_bundled_subscriptions()),s={};return n&&b.each(n,function(t,e){var i,_=e.bundled_item_id;return 0===a.quantities[_]||"variable-subscription"===e.get_product_type()&&""===e.get_variation_id()||(e=a.recurring_keys[_],i=a["bundled_item_"+_+"_recurring_totals"],void(void 0===s[e]?s[e]={html:a.recurring_html[_],price:i.price,regular_price:i.regular_price,price_incl_tax:i.price_incl_tax,price_excl_tax:i.price_excl_tax}:(s[e].price+=i.price,s[e].regular_price+=i.regular_price,s[e].price_incl_tax+=i.price_incl_tax,s[e].price_excl_tax+=i.price_excl_tax)))}),e=_,_=m.filters.apply_filters("bundle_totals",[_,a,m]),!(i=!(i=m.totals_changed(a.totals,_))&&n&&JSON.stringify(a.recurring_totals)!==JSON.stringify(s)?!0:i)&&!1!==m.is_initialized||(a.subtotals=e,a.totals=_,a.recurring_totals=s,void 0!==t)||this.updated_totals(),a},this.updated_totals=function(){clearTimeout(m.update_price_timer),m.update_price_timer=setTimeout(function(){m.updated_totals_task()},5)},this.get_price_html=function(t){var t=void 0===t?m.price_data:t,e=!1,i=m.is_composited()?m.composite_data.component.get_selected_quantity():1,_="p",a=(m.has_addons()&&(a=(t=b.extend(e=!0,{},t)).addons_price||m.get_addons_raw_price(),n=t.addons_regular_price||a,0<a&&(t.base_price=Number(t.base_price)+Number(a)),0<n)&&(t.base_regular_price=Number(t.base_regular_price)+Number(n)),(e=m.is_composited()&&(_="span","yes"===t.composited_totals_incl_qty)?!0:e)&&(t=m.calculate_subtotals(!1,t,i),t=m.calculate_totals(t)),""),n="yes"===t.show_total_string&&wc_bundle_params.i18n_total?'<span class="total">'+wc_bundle_params.i18n_total+"</span>":"",e=0===t.totals.price&&"yes"===t.show_free_string?wc_bundle_params.i18n_free:wc_pb_price_format(t.totals.price),i=wc_pb_price_format(t.totals.regular_price),s=m.get_formatted_price_suffix(t),i=(t.totals.regular_price>t.totals.price&&(e=wc_bundle_params.i18n_strikeout_price_string.replace("%f",i).replace("%t",e)),a=wc_bundle_params.i18n_price_format.replace("%t",n).replace("%p",e).replace("%s",s),m.get_recurring_price_html());return a=i?0<t.totals.regular_price?"<"+_+' class="price">'+t.price_string_recurring_up_front.replace("%s",a).replace("%r",i)+"</"+_+">":"<"+_+' class="price">'+t.price_string_recurring.replace("%r",i)+"</"+_+">":"<"+_+' class="price">'+a+"</"+_+">"},this.get_recurring_price_html=function(t){var n=void 0===t?m.price_data:t,s="";if(m.get_bundled_subscriptions()){var e,d=0<n.totals.regular_price,r=[];for(e in n.recurring_totals)n.recurring_totals.hasOwnProperty(e)&&r.push(n.recurring_totals[e]);b.each(r,function(t,e){var i=0==e.price?wc_bundle_params.i18n_free:wc_pb_price_format(e.price),_=wc_pb_price_format(e.regular_price),a=m.get_formatted_price_suffix(n,{price_incl_tax:e.price_incl_tax,price_excl_tax:e.price_excl_tax});e.regular_price>e.price&&(i=wc_bundle_params.i18n_strikeout_price_string.replace("%f",_).replace("%t",i)),_=wc_bundle_params.i18n_price_format.replace("%t","").replace("%p",i).replace("%s",a),_='<span class="bundled_sub_price_html">'+e.html.replace("%s",_)+"</span>",s=t===r.length-1||0===t&&!d?0<t||d?wc_bundle_params.i18n_recurring_price_join_last.replace("%r",s).replace("%c",_):_:wc_bundle_params.i18n_recurring_price_join.replace("%r",s).replace("%c",_)})}return s},this.show_price_html=function(){var i;return!!m.showing_price_html||(i=wc_pb_number_round(m.price_data.totals.price)!==wc_pb_number_round(m.price_data.raw_bundle_price_min)||m.price_data.raw_bundle_price_min!==m.price_data.raw_bundle_price_max,m.get_bundled_subscriptions()&&b.each(m.bundled_items,function(t,e){if(0<m.price_data.recurring_prices[e.bundled_item_id]&&0<m.price_data.quantities[e.bundled_item_id]&&(e.is_subscription("variable")||e.is_optional()||e.$self.find(".quantity input[type!=hidden]").length))return!(i=!0)}),i&&b.each(m.bundled_items,function(t,e){if(e.is_unavailable()&&e.is_required())return i=!1}),i||b.each(m.bundled_items,function(t,e){"yes"===m.price_data.has_variable_quantity[e.bundled_item_id]&&0<m.price_data["bundled_item_"+e.bundled_item_id+"_totals"].price&&(i=!0)}),m.is_composited()&&(i||m.composite_data.composite.api.is_component_priced_individually(this.composite_data.component.step_id)&&(i=!0),!i||!1!==this.composite_data.component.is_selected_product_price_visible()&&!1!==m.composite_data.composite.api.is_component_priced_individually(this.composite_data.component.step_id)||(i=!1)),i&&(m.showing_price_html=!0),i)},this.updated_totals_task=function(){var t=m.show_price_html();(m.passes_validation()||"no"===m.price_data.hide_total_on_validation_fail)&&t?(t=m.get_price_html(),t=m.filters.apply_filters("bundle_total_price_html",[t,m]),m.$bundle_price.html(t),m.$bundle_price.slideDown(200)):m.$bundle_price.slideUp(200),m.$bundle_data.trigger("woocommerce-product-bundle-updated-totals",[m])},this.updated_addons_handler=function(){m.updated_totals_task()},this.has_addons=function(){return this.$addons_totals&&0<this.$addons_totals.length},this.has_pct_addons=function(t){var t=void 0!==t?t:this,i=!1;return t.has_addons&&(t=t.$addons_totals.data("price_data"),b.each(t,function(t,e){if("percentage_based"===e.price_type)return!(i=!0)})),i},this.get_addons_raw_price=function(n,t){var s=void 0!==n,d="regular"===t?"regular":"",t=s?n:this,r=s?n.get_quantity():1,o=s?m.price_data.prices_tax[n.bundled_item_id]:m.price_data.base_price_tax,c=0;if(!t.has_addons())return 0;if(!r)return 0;if(s&&n.is_variable_product_type()&&""===n.get_variation_id())return 0;if(s&&!n.is_priced_individually())return 0;m.is_composited()&&(r=m.composite_data.component.get_selected_quantity());t=t.$addons_totals.data("price_data");return b.each(t,function(t,e){var i,_,a;e.is_custom_price?(_=0,a=!(!o||void 0===o.incl)&&Number(o.incl),i=!(!o||void 0===o.excl)&&Number(o.excl),_="incl"===wc_bundle_params.tax_display_shop&&"no"===wc_bundle_params.prices_include_tax?e.cost_raw/(a||1):"excl"===wc_bundle_params.tax_display_shop&&"yes"===wc_bundle_params.prices_include_tax?e.cost_raw/(i||1):e.cost_raw,c+=_/r):"quantity_based"===e.price_type?c+=e.cost_raw_pu:"flat_fee"===e.price_type?c+=e.cost_raw/r:"percentage_based"===e.price_type&&(a="regular"==d?s?m.price_data.regular_prices[n.bundled_item_id]:m.price_data.base_regular_price:s?m.price_data.prices[n.bundled_item_id]:m.price_data.base_price,c+=e.cost_raw_pct*a)}),c},this.totals_changed=function(t,e){return void 0===t||t.price!==e.price||t.regular_price!==e.regular_price||t.price_incl_tax!==e.price_incl_tax||t.price_excl_tax!==e.price_excl_tax},this.is_composited=function(){return!1!==this.composite_data},this.get_formatted_price_suffix=function(t,e){var t=void 0===t?m.price_data:t,i="";return e=void 0===e?t.totals:e,i=t.suffix_exists&&(i=t.suffix,t.suffix_contains_price_incl&&(i=i.replace("{price_including_tax}",wc_pb_price_format(e.price_incl_tax))),t.suffix_contains_price_excl)?i.replace("{price_excluding_tax}",wc_pb_price_format(e.price_excl_tax)):i},this.get_bundled_subscriptions=function(i){var _={},a=!1;return b.each(m.bundled_items,function(t,e){e.is_subscription(i)&&e.is_priced_individually()&&(_[t]=e,a=!0)}),!!a&&_},this.add_validation_message=function(t){this.validation_messages.push(t.toString())},this.get_validation_messages=function(){return this.validation_messages},this.passes_validation=function(){return!(0<this.validation_messages.length)},this.group_mode_supports=function(t){return-1<b.inArray(t,this.price_data.group_mode_features)}}function e(n,t,e){this.initialize=function(){this.$self=t,this.$bundled_item_cart=t.find(".cart"),this.$bundled_item_content=t.find(".bundled_item_optional_content, .bundled_item_cart_content"),this.$bundled_item_image=t.find(".bundled_product_images"),this.$bundled_item_title=t.find(".bundled_product_title_inner"),this.$bundled_item_qty=t.find("input.bundled_qty"),this.$addons_totals=t.find("#product-addons-total"),this.$nyp=t.find(".nyp"),this.$attribute_select=!1,this.$attribute_select_config=!1,this.$reset_bundled_variations=!1,this.render_addons_totals_timer=!1,this.show_addons_totals=!1,this.addons_totals_html="",this.bundled_item_index=e,this.bundled_item_id=this.$bundled_item_cart.data("bundled_item_id"),this.bundled_item_title=this.$bundled_item_cart.data("title"),this.bundled_item_title_raw=this.bundled_item_title?b("<div/>").html(this.bundled_item_title).text():"",this.bundled_item_product_title=this.$bundled_item_cart.data("product_title"),this.bundled_item_product_title_raw=this.bundled_item_title?b("<div/>").html(this.bundled_item_title).text():"",this.bundled_item_optional_suffix=void 0===this.$bundled_item_cart.data("optional_suffix")?wc_bundle_params.i18n_optional:this.$bundled_item_cart.data("optional_suffix"),this.product_type=this.$bundled_item_cart.data("type"),this.product_id=void 0===n.price_data.product_ids[this.bundled_item_id]?"":n.price_data.product_ids[this.bundled_item_id].toString(),this.nyp=void 0!==n.price_data.product_ids[this.bundled_item_id]&&"yes"===n.price_data.is_nyp[this.bundled_item_id],this.sold_individually=void 0!==n.price_data.product_ids[this.bundled_item_id]&&"yes"===n.price_data.is_sold_individually[this.bundled_item_id],this.priced_individually=void 0!==n.price_data.product_ids[this.bundled_item_id]&&"yes"===n.price_data.is_priced_individually[this.bundled_item_id],this.variation_id="",this.has_wc_core_gallery_class=this.$bundled_item_image.hasClass("images"),void 0===this.bundled_item_id&&(this.bundled_item_id=this.$bundled_item_cart.attr("data-bundled-item-id")),this.initialize_addons()},this.initialize_addons=function(){this.has_addons()?1==this.$addons_totals.data("show-sub-total")&&(this.$addons_totals.data("show-sub-total",0),this.show_addons_totals=!0,this.$bundled_item_cart.trigger("woocommerce-product-addons-update")):this.$addons_totals=!1},this.get_bundle=function(){return n},this.get_title=function(t){return(t=void 0!==t&&t)?this.bundled_item_title_raw:this.bundled_item_title},this.get_product_title=function(t){return(t=void 0!==t&&t)?this.bundled_item_product_title_raw:this.bundled_item_product_title},this.get_optional_suffix=function(){return this.bundled_item_optional_suffix},this.get_product_id=function(){return this.product_id},this.get_variation_id=function(){return this.variation_id},this.set_variation_id=function(t){this.variation_id=t.toString()},this.get_variation_data=function(){return this.$bundled_item_cart.data("product_variations")},this.get_product_type=function(){return this.product_type},this.is_variable_product_type=function(){return"variable"===this.product_type||"variable-subscription"===this.product_type},this.get_quantity=function(){var t=this.$bundled_item_qty.val();return isNaN(t)?0:parseInt(t,10)},this.get_selected_quantity=function(){return n.price_data.quantities[this.bundled_item_id]},this.get_available_quantity=function(){return n.price_data.quantities_available[this.bundled_item_id]},this.is_in_stock=function(){return"no"!==n.price_data.is_in_stock[this.bundled_item_id]},this.has_insufficient_stock=function(){return!(!this.is_selected()||0===this.get_selected_quantity()||this.is_variable_product_type()&&""===this.get_variation_id()||this.is_in_stock()&&!(""!==this.get_available_quantity()&&this.get_selected_quantity()>this.get_available_quantity())||this.backorders_allowed())},this.is_backordered=function(){return!(!this.is_selected()||0===this.get_selected_quantity()||this.is_variable_product_type()&&""===this.get_variation_id()||!((""===this.get_available_quantity()||this.get_selected_quantity()>this.get_available_quantity())&&this.backorders_allowed()&&this.backorders_require_notification()))},this.backorders_allowed=function(){return"yes"===n.price_data.backorders_allowed[this.bundled_item_id]},this.backorders_require_notification=function(){return"yes"===n.price_data.backorders_require_notification[this.bundled_item_id]},this.is_optional=function(){return"yes"===this.$bundled_item_cart.data("optional")||1===this.$bundled_item_cart.data("optional")},this.is_unavailable=function(){return"yes"===this.$bundled_item_cart.data("custom_data").is_unavailable},this.is_required=function(){return!this.is_optional()&&"no"!==this.$bundled_item_cart.data("custom_data").is_required},this.is_visible=function(){return"yes"===this.$bundled_item_cart.data("visible")||1===this.$bundled_item_cart.data("visible")},this.is_selected=function(){var t=!0;return t=this.is_optional()&&!1===this.$bundled_item_cart.data("optional_status")?!1:t},this.set_selected=function(t){this.is_optional()&&(this.$bundled_item_cart.data("optional_status",t),this.is_nyp())&&this.$nyp.data("optional_status",t)},this.init_scripts=function(){"undefined"!=typeof PhotoSwipe&&"yes"===wc_bundle_params.photoswipe_enabled&&this.init_photoswipe(),this.$self.find(".bundled_product_optional_checkbox input").trigger("change"),this.$self.find("input.bundled_qty").trigger("change"),this.is_variable_product_type()&&!this.$bundled_item_cart.hasClass("variations_form")&&(this.$reset_bundled_variations=this.$bundled_item_cart.find(".reset_bundled_variations"),0===this.$reset_bundled_variations.length&&(this.$reset_bundled_variations=!1),this.$bundled_item_cart.addClass("variations_form").wc_variation_form(),this.$attribute_select=this.$bundled_item_cart.find(".variations .attribute_options select"),this.$attribute_select_config=this.$attribute_select.filter(function(){return!1===b(this).parent().hasClass("bundled_variation_attribute_options_wrapper")}),0<this.$attribute_select.length)&&this.$attribute_select.first().trigger("change"),this.$self.find("div").stop(!0,!0),this.update_selection_title()},this.init_photoswipe=function(){b.fn.wc_product_gallery?this.$bundled_item_image.wc_product_gallery({zoom_enabled:"yes"===wc_bundle_params.zoom_enabled,flexslider_enabled:!1}):window.console.warn("Failed to initialize PhotoSwipe for bundled item images. Your theme declares PhotoSwipe support, but function '$.fn.wc_product_gallery' is undefined.");var t=this.$bundled_item_image.find("a.placeholder_image");0<t.length&&t.on("click",function(){return!1})},this.update_selection_title=function(t){var e,i,_,a;return 0!==this.$bundled_item_title.length&&(e=parseInt(this.get_quantity(),10),!isNaN(e))&&(i="hidden"===(a=this.$bundled_item_qty.attr("type"))?e:parseInt(this.$bundled_item_qty.attr("min"),10),_="hidden"===a?e:parseInt(this.$bundled_item_qty.attr("max"),10),i=isNaN(i)?-9999:i,_=isNaN(_)?9999:_,t=void 0!==t&&t,(t=this.is_selected()?t:!0)&&(e="hidden"===a?e:parseInt(this.$bundled_item_qty.attr("min"),10)),"tabular"===n.price_data.layout&&(i=_=""),a=!1,t?a=i===_&&1<e:0<e&&(a=i!==_||1<e||"yes"===wc_bundle_params.force_selection_qty),t=this.bundled_item_title,i=a?wc_bundle_params.i18n_qty_string.replace("%s",e):"",_=this.is_optional()&&""!==this.get_optional_suffix()?wc_bundle_params.i18n_optional_string.replace("%s",this.get_optional_suffix()):"",a=wc_bundle_params.i18n_title_string.replace("%t",t).replace("%q",i).replace("%o",_),void this.$bundled_item_title.html(a))},this.reset_selection_title=function(){this.update_selection_title(!0)},this.is_subscription=function(t){return"simple"===t?"subscription"===this.product_type:"variable"!==t&&"subscription"===this.product_type||"variable-subscription"===this.product_type},this.has_addons=function(){return this.$addons_totals&&0<this.$addons_totals.length},this.has_pending_required_addons=function(){var t=!1,e=this.addons_form;return e&&(e=e.validation.getValidationState(),b.each(e,function(){if(!this.validation&&"required"===this.reason)return!(t=!0)})),t},this.update_addons_prices=function(){var t=n.get_addons_raw_price(this),e=n.has_pct_addons(this)?n.get_addons_raw_price(this,"regular"):t;n.price_data.addons_prices[this.bundled_item_id]===t&&n.price_data.regular_addons_prices[this.bundled_item_id]===e||(n.price_data.addons_prices[this.bundled_item_id]=t,n.price_data.regular_addons_prices[this.bundled_item_id]=e)},this.render_addons_totals=function(){var t=this;clearTimeout(this.render_addons_totals_timer),this.render_addons_totals_timer=setTimeout(function(){t.render_addons_totals_task()},10)},this.render_addons_totals_task=function(){var t,e,i;this.has_addons&&(i=n.price_data.addons_prices[this.bundled_item_id],this.show_addons_totals)&&((!this.is_variable_product_type()||""!==this.get_variation_id())&&(e=this.get_quantity(),t=n.price_data.prices_tax[this.bundled_item_id],0<n.get_taxed_totals(i,i,t,e).price)?(i=Number(n.price_data.prices[this.bundled_item_id])+Number(i),t=wc_pb_price_format((i=n.get_taxed_totals(i,i,t,e)).price),e=n.get_formatted_price_suffix(n.price_data,i),i='<span class="price"><span class="subtotal">'+wc_bundle_params.i18n_subtotal+"</span>"+t+e+"</span>",this.addons_totals_html=i,this.$addons_totals.html(i).slideDown(200)):this.$addons_totals.slideUp(200))},this.has_single_variation=function(){return void 0!==this.get_variation_data()&&1===this.get_variation_data().length},this.has_configurable_attributes=function(){return 0<this.$attribute_select_config.length},this.reset_variation_image=function(){return n.filters.apply_filters("bundled_item_reset_variation_image",[this.is_optional()&&!this.is_selected()&&this.has_configurable_attributes(),this])},this.is_nyp=function(){return this.nyp},this.is_nyp_valid=function(){var t,e=!0;return e=b.fn.wc_nyp_get_script_object&&(t=this.$nyp.wc_nyp_get_script_object())&&!1===t.isValid()?!1:e},this.is_sold_individually=function(){return this.sold_individually},this.is_priced_individually=function(){return this.priced_individually},this.maybe_add_wc_core_gallery_class=function(){this.has_wc_core_gallery_class||this.$bundled_item_image.addClass("images")},this.maybe_remove_wc_core_gallery_class=function(){this.has_wc_core_gallery_class||this.$bundled_item_image.removeClass("images")},this.initialize()}function i(){var a=this,s={},n={add_filter:function(t,e,i,_){e={callback:e,priority:i,context:_},i=(i=s[t])?(i.push(e),this.sort_filters(i)):[e];s[t]=i},remove_filter:function(t,e,i){var _,a,n;if(s[t])if(e)if(_=s[t],i)for(n=_.length;n--;)(a=_[n]).callback===e&&a.context===i&&_.splice(n,1);else for(n=_.length;n--;)_[n].callback===e&&_.splice(n,1);else s[t]=[]},sort_filters:function(t){for(var e,i,_,a=1,n=t.length;a<n;a++){for(e=t[a],i=a;(_=t[i-1])&&_.priority>e.priority;)t[i]=t[i-1],--i;t[i]=e}return t},apply_filters:function(t,e){var i,_,a=s[t];if(a)for(_=a.length,i=0;i<_;i++)e[0]=a[i].callback.apply(a[i].context,e);return e[0]}};this.add_filter=function(t,e,i,_){return"string"==typeof t&&"function"==typeof e&&(i=parseInt(i||10,10),n.add_filter(t,e,i,_)),a},this.apply_filters=function(t,e){if("string"==typeof t)return n.apply_filters(t,e)},this.remove_filter=function(t,e){return"string"==typeof t&&n.remove_filter(t,e),a}}jQuery(function(r){r("body").on("quick-view-displayed",function(){r(".quick-view .bundle_form .bundle_data").each(function(){var t=r(this);0===t.closest(".composite_form").length&&t.wc_pb_bundle_form()})}),r.fn.wc_pb_bundle_form=function(){if(!r(this).hasClass("bundle_data"))return!0;var t=r(this),e=t.data("bundle_id");if(void 0===e){if(!(e=t.attr("data-bundle-id")))return!1;t.data("bundle_id",e)}var i,_,a=t.closest(".bundle_form"),n=a.closest(".composite_form"),s=!1,d=e;(is_part_of_composite=0<n.length)&&0<(i=a.closest(".component").data("item_id"))&&r.fn.wc_get_composite_script&&!1!==(n=n.wc_get_composite_script())&&!1!==(_=n.api.get_step(i))&&(s={composite:n,component:_},d=i),void 0!==wc_pb_bundle_scripts[d]&&(is_part_of_composite?wc_pb_bundle_scripts[d].shutdown():d=d+"_"+Object.keys(wc_pb_bundle_scripts).length),wc_pb_bundle_scripts[d]=new o({$bundle_form:a,$bundle_data:t,bundle_id:e,composite_data:s}),a.data("script_id",d),wc_pb_bundle_scripts[d].initialize()},r(".bundle_form .bundle_data").each(function(){var t=r(this);0===t.closest(".composite_form").length&&t.wc_pb_bundle_form()})})}(jQuery);