jQuery(function(r){function n(i){var s=this;this.$element=i,this.$content=i.find("div.item-data"),this.$discount=this.$content.find(".discount"),this.$visibility=this.$content.find(".item_visibility"),this.$price_visibility=this.$content.find(".price_visibility"),this.$allowed_variations=this.$content.find("div.allowed_variations"),this.$default_variation_attributes=this.$content.find("div.default_variation_attributes"),this.$custom_title=this.$content.find("div.custom_title"),this.$custom_description=this.$content.find("div.custom_description"),this.$override_title=this.$content.find(".override_title"),this.$override_description=this.$content.find(".override_description"),this.$hide_thumbnail=this.$content.find(".hide_thumbnail"),this.$section_links=this.$content.find(".subsubsub a"),this.$sections=this.$content.find(".options_group"),this.$priced_individually_input=this.$content.find(".priced_individually input"),this.$override_variations_input=this.$content.find(".override_variations input"),this.$override_defaults_input=this.$content.find(".override_default_variation_attributes input"),this.$override_title_input=this.$override_title.find("input"),this.$override_description_input=this.$override_description.find("input"),this.$price_visibility_product_input=this.$price_visibility.find("input.price_visibility_product"),this.$price_visibility_cart_input=this.$price_visibility.find("input.price_visibility_cart"),this.$price_visibility_order_input=this.$price_visibility.find("input.price_visibility_order"),this.$visibility_product_input=this.$visibility.find("input.visibility_product"),this.$visibility_cart_input=this.$visibility.find("input.visibility_cart"),this.$visibility_order_input=this.$visibility.find("input.visibility_order"),this.$min_qty=this.$content.find(".quantity_min"),this.$min_qty_input=this.$min_qty.find("input.item_quantity"),this.$max_qty=this.$content.find(".quantity_max"),this.$max_qty_input=this.$max_qty.find("input.item_quantity"),this.$default_qty=this.$content.find(".quantity_default"),this.$default_qty_input=this.$default_qty.find("input.item_quantity"),this.$optional_checkbox=this.$content.find("div.optional"),this.$optional_checkbox_input=this.$optional_checkbox.find("input.checkbox"),this.max_qty_prev=this.$max_qty_input.val(),this.default_qty_prev=this.$default_qty_input.val(),this.add_error_tip=function(i,t){var e=i.position();0===i.parent().find(".wc_error_tip").length?(i.after('<div class="wc_error_tip">'+t+"</div>"),i.parent().find(".wc_error_tip").css("left",e.left+i.width()-i.width()/2-r(".wc_error_tip").width()/2).css("top",e.top+i.height()+4).fadeIn("100")):i.parent().find(".wc_error_tip").html(t)},this.remove_error_tip=function(i){i.parent().find(".wc_error_tip").fadeOut("100",function(){r(this).remove()})},this.maybe_hide_optional_checkbox=function(i){"min"===i&&(0<s.$min_qty_input.val()?(s.$optional_checkbox.show(),s.$optional_checkbox_input.prop("disabled",!1)):s.$optional_checkbox.data("is_optional_qty_zero")&&"no"===s.$optional_checkbox.data("is_optional_qty_zero")&&(s.$optional_checkbox.hide(),s.$optional_checkbox_input.prop("disabled",!0)))},this.validate_quantity=function(i,t){var e=s.$min_qty_input,n=("max"===i?e=s.$max_qty_input:"default"===i&&(e=s.$default_qty_input),e.val()),d=parseFloat(e.attr("min")),o=parseFloat(e.attr("max")),e=parseFloat(e.attr("step")),a={qty:n,error:""};return 0<=d&&(n<d||isNaN(n))?"max"===i&&""===n||(a.qty="max"===t?s.max_qty_prev:"default"===t?s.default_qty_prev:d,a.error=wc_bundles_admin_params.i18n_qty_low_error.replace("%s",d)):0<o&&o<n?(a.qty="default"===t?s.default_qty_prev:o,a.error=wc_bundles_admin_params.i18n_qty_high_error.replace("%s",o)):0<e&&0<n&&n%e&&(a.qty=e*Math.ceil(n/e),a.error=wc_bundles_admin_params.i18n_qty_step_error.replace("%s",e)),a},this.priced_individually_input_changed=function(){s.$priced_individually_input.is(":checked")?(s.$discount.show(),s.$price_visibility.show()):(s.$discount.hide(),s.$price_visibility.hide())},this.override_variations_input_changed=function(){s.$override_variations_input.is(":checked")?s.$allowed_variations.show():s.$allowed_variations.hide()},this.override_defaults_input_changed=function(){s.$override_defaults_input.is(":checked")?s.$default_variation_attributes.show():s.$default_variation_attributes.hide()},this.override_title_input_changed=function(){s.$override_title_input.is(":checked")?s.$custom_title.show():s.$custom_title.hide()},this.override_description_input_changed=function(){s.$override_description_input.is(":checked")?s.$custom_description.show():s.$custom_description.hide()},this.visibility_product_input_changed=function(){s.$visibility_product_input.is(":checked")?(s.$override_title.show(),s.$override_description.show(),s.$hide_thumbnail.show(),s.override_title_input_changed(),s.override_description_input_changed()):(s.$override_title.hide(),s.$override_description.hide(),s.$hide_thumbnail.hide(),s.$custom_description.hide(),s.$custom_title.hide())},this.toggled_visibility=function(i){s["$visibility_"+i+"_input"].is(":checked")?s["$price_visibility_"+i+"_input"].css("opacity",1):s["$price_visibility_"+i+"_input"].css("opacity",.5)},this.section_changed=function(i){s.$section_links.removeClass("current"),i.addClass("current"),s.$sections.addClass("options_group_hidden"),s.$content.find(".options_group_"+i.data("tab")).removeClass("options_group_hidden")},this.initialize=function(){s.priced_individually_input_changed(),s.override_variations_input_changed(),s.override_defaults_input_changed(),s.override_title_input_changed(),s.override_description_input_changed(),s.visibility_product_input_changed(),s.toggled_visibility("product"),s.toggled_visibility("cart"),s.toggled_visibility("order"),s.$element.sw_select2()},this.initialize()}var d,i,t=r("p._wc_pb_edit_in_cart_field"),o=r("select#product-type"),e=r("select#_wc_pb_group_mode"),a=r("#bundled_product_data"),s=a.find(".wc-metaboxes-wrapper"),_=a.find(".toolbar"),c=r(".wc-bundled-items"),l=r(".wc-bundled-item",c),u=r("#bundled_product",a),p={},h=l.length,v={message:null,overlayCSS:{background:"#fff",opacity:.6}},f=a.parent().find("#shipping_product_data"),b=r("input#_virtual"),m=b.prop("checked"),y=!1,g=r("input#_virtual_bundle"),$=f.find(".options_group.bundle_type"),w=$.find(".bundle_type_options button"),k=r("#_sold_individually").closest(".form-field");function q(){0<l.length?(s.removeClass("no-items"),_.removeClass("disabled")):(s.addClass("no-items"),_.addClass("disabled"))}w.find(".woocommerce-help-tip").attr("tabindex",-1),r.fn.wc_bundles_select2=function(){r(document.body).trigger("wc-enhanced-select-init")},r(".bundle_stock_msg").appendTo("._manage_stock_field .description"),k.addClass("hide_if_bundle"),k.siblings(".woocommerce-help-tip").addClass("hide_if_bundle"),r("#linked_product_data .grouping.show_if_simple, #linked_product_data .form-field.show_if_grouped").addClass("hide_if_bundle"),r(".show_if_simple:not(.hide_if_bundle)").addClass("show_if_bundle"),r("body").on("woocommerce-product-type-change",function(i,t){"bundle"===t&&(r(".show_if_external").hide(),r(".show_if_bundle").show(),r("input#_manage_stock").trigger("change"),r("#_nyp").trigger("change"),y&&g.prop("checked",b.prop("checked")),g.prop("checked")||b.prop("checked",!1).change(),"unassembled"===w.find("input.bundle_type_option:checked").first().val())&&(f.addClass("bundle_unassembled"),a.addClass("bundle_unassembled"))}),r("form#post").on("submit",function(){var i,t,e;"bundle"===o.val()&&(i=r(this),t=r('<input type="hidden" name="pb_post_control_var" value="1"/>'),e=r('<input type="hidden" name="pb_post_test_var" value="1"/>'),i.prepend(t),i.append(e))}),e.on("change",function(){-1===r.inArray(e.val(),wc_bundles_admin_params.group_modes_with_parent)?t.hide():t.show()}),r("input#_downloadable").on("change",function(){o.trigger("change")}),u.on("change",function(){var i,t=u.val(),t=!!(t&&0<t.length)&&t.shift();return t&&(u.val([]).trigger("change"),a.block(v),h++,i={action:"woocommerce_add_bundled_product",post_id:woocommerce_admin_meta_boxes.post_id,id:h,product_id:t,security:wc_bundles_admin_params.add_bundled_product_nonce},setTimeout(function(){r.post(woocommerce_admin_meta_boxes.ajax_url,i,function(i){var t,e;""!==i.markup?(c.append(i.markup),t=r(".wc-bundled-item",c).last(),e="bundled_item_"+h,t.data("bundled_item_id",e),p[e]=new n(t),a.triggerHandler("wc-bundled-products-changed"),t.find(".woocommerce-help-tip").tipTip({attribute:"data-tip",fadeIn:50,fadeOut:50,delay:200}),t.wc_bundles_select2(),a.trigger("wc-bundles-added-bundled-product")):""!==i.message&&window.alert(i.message),u.selectSW("open"),u.selectSW("close"),a.unblock()})},250)),!1}),s.on("click",".expand_all",function(){return r(this).hasClass("disabled")||r.each(p,function(i,t){t.$element.addClass("open").removeClass("closed")}),!1}).on("click",".close_all",function(){return r(this).hasClass("disabled")||r.each(p,function(i,t){t.$element.addClass("closed").removeClass("open")}),!1}),a.on("wc-bundled-products-changed",function(){(l=r(".wc-bundled-item",c)).each(function(i,t){r(".item_menu_order",t).val(i)}),q()}).one("wc-bundles-added-bundled-product",function(){s.removeClass("wc-bundle-metaboxes-wrapper--boarding")}),c.on("input change","input.item_quantity",function(i){var t,e,n,d=r(this),o=d.closest(".wc-bundled-item").data("bundled_item_id"),a=p[o],o="min",s=(d.hasClass("item_quantity_max")?o="max":d.hasClass("item_quantity_default")&&(o="default"),a.validate_quantity(o,o));s.error?"input"===event.type?setTimeout(function(){a.add_error_tip(d,s.error)},5):d.val(s.qty).change():(a.remove_error_tip(d),"change"===event.type&&(t=a.$min_qty_input.val(),e=a.$max_qty_input.val(),n=a.$default_qty_input.val(),a.$max_qty_input.attr("min",t),"default"!==o&&((s=a.validate_quantity("max",o)).error&&(a.$max_qty_input.val(s.qty),e=s.qty),a.max_qty_prev=e),a.$default_qty_input.attr("min",t),a.$default_qty_input.attr("max",e),(s=a.validate_quantity("default",o)).error&&(a.$default_qty_input.val(s.qty),n=s.qty),a.default_qty_prev=n,a.maybe_hide_optional_checkbox(o)))}).on("click","a.edit-product",function(i){i.stopPropagation()}).on("click","a.remove_row",function(i){var t=r(this).closest(".wc-bundled-item"),e=t.data("bundled_item_id");t.find("*").off(),t.remove(),delete p[e],a.triggerHandler("wc-bundled-products-changed"),i.preventDefault()}).on("change",".priced_individually input",function(){var i=r(this).closest(".wc-bundled-item").data("bundled_item_id");p[i].priced_individually_input_changed()}).on("change",".override_variations input",function(){var i=r(this).closest(".wc-bundled-item").data("bundled_item_id");p[i].override_variations_input_changed()}).on("change",".override_default_variation_attributes input",function(){var i=r(this).closest(".wc-bundled-item").data("bundled_item_id");p[i].override_defaults_input_changed()}).on("change",".override_title input",function(){var i=r(this).closest(".wc-bundled-item").data("bundled_item_id");p[i].override_title_input_changed()}).on("change",".override_description input",function(){var i=r(this).closest(".wc-bundled-item").data("bundled_item_id");p[i].override_description_input_changed()}).on("change","input.visibility_product",function(){var i=r(this).closest(".wc-bundled-item").data("bundled_item_id"),i=p[i];i.visibility_product_input_changed(),i.toggled_visibility("product")}).on("change","input.visibility_cart",function(){var i=r(this).closest(".wc-bundled-item").data("bundled_item_id");p[i].toggled_visibility("cart")}).on("change","input.visibility_order",function(){var i=r(this).closest(".wc-bundled-item").data("bundled_item_id");p[i].toggled_visibility("order")}).on("click",".subsubsub a",function(i){var t=r(this),e=r(this).closest(".wc-bundled-item").data("bundled_item_id");p[e].section_changed(t),i.preventDefault()}),l.each(function(i){var t=r(this),i="bundled_item_"+i;t.data("bundled_item_id",i),p[i]=new n(t)}),c.sortable({items:".wc-bundled-item",cursor:"move",axis:"y",handle:".sort-item",scrollSensitivity:40,forcePlaceholderSize:!0,helper:"clone",opacity:.65,placeholder:"wc-metabox-sortable-placeholder",start:function(i,t){t.item.css("background-color","#f6f6f6")},stop:function(i,t){t.item.removeAttr("style"),a.triggerHandler("wc-bundled-products-changed")}}),q(),$.detach().prependTo(f),f.find(".form-field._weight_field").after($.find(".form-field.bundle_aggregate_weight_field")),g.on("change",function(){var i=g.prop("checked");b.prop("checked")!==i&&b.prop("checked",i).trigger("change"),i?a.addClass("bundle_virtual"):a.removeClass("bundle_virtual")}),b.on("change",function(){b.prop("checked")!==m&&(y=!0)}),w.on("click",function(){var i=r(this),t=i.find("input").prop("checked","checked").val();w.removeClass("selected"),w.attr("aria-checked",!1),i.addClass("selected"),i.attr("aria-checked",!0),"assembled"===t?(f.removeClass("bundle_unassembled"),a.removeClass("bundle_unassembled")):"unassembled"===t&&(f.addClass("bundle_unassembled"),a.addClass("bundle_unassembled"))}),w.on("focus",function(){var i=r(this);w.attr("tabindex",-1),i.attr("tabindex",0)}),w.on("keydown",function(i){"ArrowRight"===i.key||"ArrowDown"===i.key?(i.preventDefault(),r(this).next().click().focus()):"ArrowLeft"!==i.key&&"ArrowUp"!==i.key||(i.preventDefault(),r(this).prev().click().focus())}),"yes"===wc_bundles_admin_params.is_first_bundle&&o.val("bundle").trigger("change").trigger("focus"),k=a.find(".add_bundled_product"),i=k.find(".sw-expanding-button"),$=r(document.body),i.length?(i.sw_select2(),i.on("click",function(i){i.stopPropagation(),clearTimeout(d);var t=r(this),e=t.find(".select2-search__field");t.addClass("sw-expanding-button--open"),d=setTimeout(function(){e.trigger("focus")},700),u.one("change",function(){t.removeClass("sw-expanding-button--open")})}),$.on("click",".select2-container",function(i){i.stopPropagation()}),$.on("click",function(){i.removeClass("sw-expanding-button--open")})):k.sw_select2(),o.trigger("change"),e.trigger("change")});