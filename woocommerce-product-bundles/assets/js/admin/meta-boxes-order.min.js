jQuery(function(c){var _=c("#woocommerce-order-items"),d=!1,a={handle_events:function(){_.on("click","button.configure_bundle",{action:"configure"},this.clicked_edit_button).on("click","button.edit_bundle",{action:"edit"},this.clicked_edit_button),c(document.body).on("click","input.bundled_product_checkbox",this.toggle_optional_item)},toggle_optional_item:function(e){var n=c(this),o=n.is(":checked"),n=n.closest(".details").find(".bundled_item_cart_content");o?n.show():n.hide()},clicked_edit_button:function(e){var n=c.WCBackboneModal.View.extend({addButton:a.clicked_done_button}),o=c(this).closest("tr.item").attr("data-order_item_id");return d=new n({target:"wc-modal-edit-bundle",string:{action:"configure"===e.data.action?wc_bundles_admin_order_params.i18n_configure:wc_bundles_admin_order_params.i18n_edit,item_id:o}}),a.populate_form(),!1},clicked_done_button:function(n){if(d.is_valid=!0,_.trigger("wc_pb_validate_form",d),!d.is_valid)return!1;var e=c.extend({},a.get_taxable_address(),{action:"woocommerce_edit_bundle_in_order",item_id:d._string.item_id,fields:d.$el.find("input, select, textarea").serialize(),dataType:"json",order_id:woocommerce_admin_meta_boxes.post_id,security:wc_bundles_admin_order_params.edit_bundle_nonce}),o=d.$el.find("form")[0],t=new FormData(o);for(const i in e)t.append(i,e[i]);a.block(d.$el.find(".wc-backbone-modal-content")),c.post({url:woocommerce_admin_meta_boxes.ajax_url,type:"POST",data:t,processData:!1,contentType:!1,cache:!1,success:function(e){e.result&&"success"===e.result?(_.find(".inside").empty(),_.find(".inside").append(e.html),_.trigger("wc_order_items_reloaded"),e.notes_html&&(c("ul.order_notes").empty(),c("ul.order_notes").append(c(e.notes_html).find("li"))),a.unblock(d.$el.find(".wc-backbone-modal-content")),a.block(_,{fadeIn:0}),setTimeout(function(){a.unblock(_)},250),d.closeButton(n)):(window.alert(c("<textarea />").html(e.error||wc_bundles_admin_order_params.i18n_validation_error).text()),a.unblock(d.$el.find(".wc-backbone-modal-content")))},error:function(){window.alert(wc_bundles_admin_order_params.i18n_validation_error),a.unblock(d.$el.find(".wc-backbone-modal-content"))}})},populate_form:function(){a.block(d.$el.find(".wc-backbone-modal-content"));var e={action:"woocommerce_configure_bundle_order_item",item_id:d._string.item_id,dataType:"json",order_id:woocommerce_admin_meta_boxes.post_id,security:wc_bundles_admin_order_params.edit_bundle_nonce};c.post(woocommerce_admin_meta_boxes.ajax_url,e,function(e){e.result&&"success"===e.result?(d.$el.find("form").html(e.html),a.unblock(d.$el.find(".wc-backbone-modal-content")),_.trigger("wc_pb_populate_form",d)):(window.alert(wc_bundles_admin_order_params.i18n_form_error),a.unblock(d.$el.find(".wc-backbone-modal-content")),d.$el.find(".modal-close").trigger("click"))})},get_taxable_address:function(){var e="",n="",o="",t="";return"shipping"===woocommerce_admin_meta_boxes.tax_based_on&&(e=c("#_shipping_country").val(),n=c("#_shipping_state").val(),o=c("#_shipping_postcode").val(),t=c("#_shipping_city").val()),"billing"!==woocommerce_admin_meta_boxes.tax_based_on&&e||(e=c("#_billing_country").val(),n=c("#_billing_state").val(),o=c("#_billing_postcode").val(),t=c("#_billing_city").val()),{country:e,state:n,postcode:o,city:t}},block:function(e,n){n=c.extend({},{message:null,overlayCSS:{background:"#fff",opacity:.6}},n||{});e.block(n)},unblock:function(e){e.unblock()}};a.handle_events()});