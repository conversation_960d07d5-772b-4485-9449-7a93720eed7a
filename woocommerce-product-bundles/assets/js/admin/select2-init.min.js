jQuery(function(s){var n=s.fn.selectSW.amd.require("selectSW/utils"),i={language:{errorLoading:function(){return wc_enhanced_select_params.i18n_searching},inputTooLong:function(e){e=e.input.length-e.maximum;return 1==e?wc_enhanced_select_params.i18n_input_too_long_1:wc_enhanced_select_params.i18n_input_too_long_n.replace("%qty%",e)},inputTooShort:function(e){e=e.minimum-e.input.length;return 1==e?wc_enhanced_select_params.i18n_input_too_short_1:wc_enhanced_select_params.i18n_input_too_short_n.replace("%qty%",e)},loadingMore:function(){return wc_enhanced_select_params.i18n_load_more},maximumSelected:function(e){return 1===e.maximum?wc_enhanced_select_params.i18n_selection_too_long_1:wc_enhanced_select_params.i18n_selection_too_long_n.replace("%qty%",e.maximum)},noResults:function(){return wc_enhanced_select_params.i18n_no_matches},searching:function(){return wc_enhanced_select_params.i18n_searching}}};function l(t){t.on("select2:unselecting",function(){t.data("unselecting",!0)}).on("select2:opening",function(e){t.data("unselecting")&&(t.removeData("unselecting"),e.preventDefault())})}function r(t,a){var e;t.data("sortable")&&t.prop("multiple")&&(e=t.next(".select2-container").find("ul.select2-selection__rendered"),a=a||{},e.sortable({placeholder:"ui-state-highlight select2-selection__choice",forcePlaceholderSize:!0,items:"li:not(.select2-search__field)",tolerance:"pointer",stop:function(){s(e.find(".select2-selection__choice").get().reverse()).each(function(){var e=n.GetData(this,"data").id,e=t.find('option[value="'+e+'"]')[0];t.prepend(e),"function"==typeof a.stop&&a.stop(t)})}}))}s.fn.sw_select2=function(c){s(":input.sw-select2",this).filter(":not(.sw-select2--initialized)").each(function(){var e=s(this),t=e.data("wrap"),a={minimumResultsForSearch:10,allowClear:!!e.data("allow_clear"),placeholder:e.data("placeholder"),closeOnSelect:!0};t&&(t="yes"!==t?"sw-select2-wrap-"+t.toString():"sw-select2-wrap",e.wrap('<div class="'+t+'"></div>')),a=s.extend(a,i),e.selectSW(a).addClass("sw-select2--initialized"),l(e),r(e)}),s(":input.sw-select2-search--products",this).filter(":not(.sw-select2--initialized)").each(function(){var t,n=s(this),e=n.data("wrap"),a={cache:!0,allowClear:!!n.data("allow_clear"),placeholder:n.data("placeholder"),minimumInputLength:n.data("minimum_input_length")?n.data("minimum_input_length"):"3",escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(e){return{term:e.term,action:n.data("action")||"woocommerce_json_search_products_and_variations",security:wc_enhanced_select_params.search_products_nonce,exclude:n.data("exclude"),exclude_type:n.data("exclude_type"),include:n.data("include"),limit:n.data("limit")}},processResults:function(e){var a=[];return"woocommerce_json_search_products_and_variations_in_component"===n.data("action")&&("yes"!==n.data("component_optional")&&"8.0"!==n.data("action_version")||a.push({id:"-1",text:wc_composite_admin_params.i18n_none}),"8.0"!==n.data("action_version"))&&a.push({id:"0",text:wc_composite_admin_params.i18n_all}),e&&s.each(e,function(e,t){a.push({id:e,text:t})}),{results:a}}}};e&&(e="yes"!==e?"sw-select2-wrap-"+e.toString():"sw-select2-wrap",n.wrap('<div class="'+e+'"></div>')),a=s.extend(a,i),n.selectSW(a).addClass("sw-select2--initialized"),l(n),r(n,c&&c.sortable?c.sortable:{}),!(t=n).data("sortable")&&t.prop("multiple")&&t.on("change",function(){var e=t.children();e.sort(function(e,t){e=e.text.toLowerCase(),t=t.text.toLowerCase();return t<e?1:e<t?-1:0}),t.html(e)})}),s(":input.sw-select2-search--categories",this).filter(":not(.sw-select2--initialized)").each(function(){var e=s(this),t=e.data("wrap"),a={cache:!0,allowClear:!!e.data("allow_clear"),placeholder:e.data("placeholder"),minimumInputLength:e.data("minimum_input_length")?e.data("minimum_input_length"):3,escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:250,data:function(e){return{term:e.term,action:"woocommerce_json_search_categories",security:wc_enhanced_select_params.search_categories_nonce}},processResults:function(e){var a=[];return e&&s.each(e,function(e,t){a.push({id:t.slug,text:t.formatted_name})}),{results:a}}}};t&&(t="yes"!==t?"sw-select2-wrap-"+t.toString():"sw-select2-wrap",e.wrap('<div class="'+t+'"></div>')),a=s.extend(a,i),e.selectSW(a).addClass("sw-select2--initialized")}),s(".sw-select2-search--customers",this).filter(":not(.sw-select2--initialized)").each(function(){var t=s(this),e=t.data("wrap"),a={cache:!0,allowClear:!!t.data("allow_clear"),placeholder:t.data("placeholder"),minimumInputLength:t.data("minimum_input_length")?t.data("minimum_input_length"):3,escapeMarkup:function(e){return e},ajax:{url:wc_enhanced_select_params.ajax_url,dataType:"json",delay:1e3,data:function(e){return{term:e.term,action:"woocommerce_json_search_customers",security:wc_enhanced_select_params.search_customers_nonce,exclude:t.data("exclude")}},processResults:function(e){var a=[];return e&&s.each(e,function(e,t){a.push({id:e,text:t})}),{results:a}},cache:!0}};e&&(e="yes"!==e?"sw-select2-wrap-"+e.toString():"sw-select2-wrap",t.wrap('<div class="'+e+'"></div>')),a=s.extend(a,i),t.selectSW(a).addClass("sw-select2--initialized")}),s(document.body).trigger("sw-select2-init")},s("html").on("wc_backbone_modal_before_remove",function(){s(".sw-select2, :input.sw-select2-search--products").filter(".select2-hidden-accessible").selectSW("close")}).on("click",function(e){this===e.target&&s(".sw-select2, :input.sw-select2-search--products").filter(".select2-hidden-accessible").selectSW("close")}),s(document.body).find(".sw-select2-autoinit").each(function(){s(this).sw_select2()})});