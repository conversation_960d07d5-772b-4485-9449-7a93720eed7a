@font-face{font-family:"FontAwesomePB";src:url("../../fonts/fa-pb-615.eot");src:url("../../fonts/fa-pb-615.eot?#iefix") format("embedded-opentype"),url("../../fonts/fa-pb-615.woff2") format("woff2"),url("../../fonts/fa-pb-615.woff2") format("woff"),url("../../fonts/fa-pb-615.ttf") format("truetype"),url("../../fonts/fa-pb-615.svg#fapbregular") format("svg");font-weight:normal;font-style:normal}.wc_pb_notice.notice-native{border-left-color:var(--wp-admin-theme-color, #2271b1) !important}table.wp-list-table .column-is_in_stock{width:14ch}.wc_pb_notice .wc_pb_forward{float:right}.woocommerce-report-table-bundles button.woocommerce-table__download-button,.woocommerce-report-table-bundles-stock button.woocommerce-table__download-button{padding:6px;color:#111;text-decoration:none;align-items:center;display:none}.woocommerce-report-table-bundles button.woocommerce-table__download-button svg,.woocommerce-report-table-bundles-stock button.woocommerce-table__download-button svg{margin-right:4px;height:24px;width:24px}@media screen and (max-width: 782px){.woocommerce-report-table-bundles button.woocommerce-table__download-button svg,.woocommerce-report-table-bundles-stock button.woocommerce-table__download-button svg{margin-right:0}.woocommerce-report-table-bundles button.woocommerce-table__download-button .woocommerce-table__download-button__label,.woocommerce-report-table-bundles-stock button.woocommerce-table__download-button .woocommerce-table__download-button__label{display:none}}