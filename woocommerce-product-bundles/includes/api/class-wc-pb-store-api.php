<?php
/**
 * WC_PB_Store_API class
 *
 * @package  WooCommerce Product Bundles
 * @since    6.15.0
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

use Automattic\WooCommerce\StoreApi\Exceptions\RouteException;
use Automattic\WooCommerce\StoreApi\Schemas\V1\CartItemSchema;
use Automattic\WooCommerce\StoreApi\Schemas\V1\ProductSchema;

/**
 * Extends the store public API with bundle related data for each bundle parent and child item.
 *
 * @version 8.3.5
 */
class WC_PB_Store_API {

	/**
	 * Stores the cart item key of the last bundled item.
	 *
	 * @var string
	 */
	private static $last_bundled_item_key;

	/**
	 * Stores the cart item key of the last bundled item.
	 *
	 * @var string
	 */
	private static $faked_parent_bundled_item_key;

	/**
	 * Plugin Identifier, unique to each plugin.
	 *
	 * @var string
	 */
	const IDENTIFIER = 'bundles';

	/**
	 * Custom REST API product field names, indicating support for getting/updating.
	 *
	 * @var array
	 */
	private static $product_fields = array(
		'bundled_by'                       => 0,
		'bundle_stock_status'              => 1,
		'bundle_stock_quantity'            => 1,
		'bundle_virtual'                   => 1,
		'bundle_layout'                    => 1,
		'bundle_add_to_cart_form_location' => 1,
		'bundle_editable_in_cart'          => 1,
		'bundle_sold_individually_context' => 1,
		'bundle_item_grouping'             => 1,
		'bundle_min_size'                  => 1,
		'bundle_max_size'                  => 1,
		'bundle_price'                     => 1,
		'bundled_items'                    => array(
			'bundled_item_id'                       => 1,
			'product_id'                            => 1,
			'menu_order'                            => 1,
			'quantity_min'                          => 1,
			'quantity_max'                          => 1,
			'quantity_default'                      => 1,
			'priced_individually'                   => 1,
			'shipped_individually'                  => 1,
			'override_title'                        => 1,
			'title'                                 => 1,
			'override_description'                  => 1,
			'description'                           => 1,
			'optional'                              => 1,
			'hide_thumbnail'                        => 1,
			'discount'                              => 1,
			'override_variations'                   => 1,
			'allowed_variations'                    => 1,
			'override_default_variation_attributes' => 1,
			'default_variation_attributes'          => 1,
			'single_product_visibility'             => 1,
			'cart_visibility'                       => 1,
			'order_visibility'                      => 1,
			'single_product_price_visibility'       => 1,
			'cart_price_visibility'                 => 1,
			'order_price_visibility'                => 1,
			'stock_status'                          => 1,
		),
	);

	/**
	 * Cache to store Store API product schema extension, as it's called multiple times in each request.
	 *
	 * @var array
	 */
	private static $product_item_schema_cache;

	/**
	 * Bootstraps the class and hooks required data.
	 */
	public static function init() {

		self::extend_store();

		// Aggregate cart item prices/subtotals and filter min/max/multipleof quantities.
		add_filter( 'rest_request_after_callbacks', array( __CLASS__, 'filter_cart_item_data' ), 10, 3 );
		add_filter( 'woocommerce_hydration_request_after_callbacks', array( __CLASS__, 'filter_cart_item_data' ), 10, 3 );

		// Validate bundles in the Store API and add cart errors.
		add_action( 'woocommerce_store_api_validate_cart_item', array( __CLASS__, 'validate_cart_item' ), 10, 2 );

		// Prevent access to the checkout block.
		add_action( 'woocommerce_store_api_checkout_update_order_meta', array( __CLASS__, 'validate_draft_order' ) );

		// Validate removal of mandatory bundled items.
		add_action( 'woocommerce_remove_mandatory_bundled_cart_item', array( __CLASS__, 'validate_mandatory_bundled_cart_item_removal' ), 10, 2 );
	}

	/**
	 * Registers the actual data into each endpoint.
	 */
	public static function extend_store() {

		if ( ! function_exists( 'woocommerce_store_api_register_endpoint_data' ) ) {
			return;
		}

		woocommerce_store_api_register_endpoint_data(
			array(
				'endpoint'        => CartItemSchema::IDENTIFIER,
				'namespace'       => self::IDENTIFIER,
				'data_callback'   => array( __CLASS__, 'extend_cart_item_data' ),
				'schema_callback' => array( __CLASS__, 'extend_cart_item_schema' ),
				'schema_type'     => ARRAY_A,
			)
		);

		woocommerce_store_api_register_endpoint_data(
			array(
				'endpoint'        => ProductSchema::IDENTIFIER,
				'namespace'       => self::IDENTIFIER,
				'data_callback'   => array( __CLASS__, 'extend_product_item_data' ),
				'schema_callback' => array( __CLASS__, 'extend_product_item_schema' ),
				'schema_type'     => ARRAY_A,
			)
		);
	}

	/**
	 * Register product bundle data into cart items endpoint.
	 *
	 * @param array $cart_item Cart item data.
	 * @return array $item_data
	 */
	public static function extend_cart_item_data( $cart_item ) {

		$item_data = array();

		if ( wc_pb_is_bundle_container_cart_item( $cart_item ) ) {

			if ( ! $cart_item['data']->is_type( 'bundle' ) ) {
				return $item_data;
			}

			$bundle = $cart_item['data'];

			// Reset last item key.
			self::$last_bundled_item_key = false;
			// Reset faked parent item key.
			self::$faked_parent_bundled_item_key = false;

			$is_price_visible        = true;
			$is_subtotal_visible     = true;
			$is_visible              = true;
			$is_cart_meta_visible    = false;
			$is_summary_meta_visible = false;

			$bundled_cart_items     = wc_pb_get_bundled_cart_items( $cart_item );
			$bundled_cart_item_keys = array_keys( $bundled_cart_items );

			// Hide entire item?
			if ( false === WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'parent_item' ) ) {
				$is_visible = false;
			}

			// Find and store last item.
			if ( $bundled_cart_items ) {
				foreach ( $bundled_cart_items as $bundled_cart_item_key => $bundled_cart_item ) {

					$bundled_item = $bundle->get_bundled_item( $bundled_cart_item['bundled_item_id'] );

					if ( $bundled_item && $bundled_item->is_visible( 'cart' ) ) {
						self::$last_bundled_item_key = $bundled_cart_item_key;
					}

					if ( $bundled_cart_item_keys[0] === $bundled_cart_item_key && WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'faked_parent_item' ) ) {
						self::$faked_parent_bundled_item_key = $bundled_cart_item_key;
					}
				}
			}

			// Hide price?
			if ( empty( $cart_item['data']->get_price() ) && false === WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'aggregated_prices' ) && WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'component_multiselect' ) ) {
				$is_price_visible = false;
			}

			// Hide subtotal?
			if ( empty( $cart_item['line_subtotal'] ) && false === WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'aggregated_subtotals' ) && WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'component_multiselect' ) ) {
				$is_subtotal_visible = false;
			}

			// Is bundled item meta visible?
			if ( WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'parent_cart_item_meta' ) ) {
				$is_cart_meta_visible    = true;
				$is_summary_meta_visible = true;
			}

			if ( WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'parent_item' ) && WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'child_item_indent' ) ) {
				$is_summary_meta_visible = true;
			}

			$item_data['bundled_items'] = $cart_item['bundled_items'];
			$item_data['bundle_data']   = array(
				'configuration'             => $cart_item['stamp'],
				'is_editable'               => $bundle->is_editable_in_cart(),
				'is_price_hidden'           => ! $is_price_visible,
				'is_subtotal_hidden'        => ! $is_subtotal_visible,
				'is_hidden'                 => ! $is_visible,
				'is_meta_hidden_in_cart'    => ! $is_cart_meta_visible,
				'is_meta_hidden_in_summary' => ! $is_summary_meta_visible,
			);

			// phpcs:ignore Squiz.PHP.DisallowMultipleAssignments.FoundInControlStructure
		} elseif ( $container_item = wc_pb_get_bundled_cart_item_container( $cart_item ) ) {

			$bundle = $container_item['data'];

			if ( ! $bundle->is_type( 'bundle' ) ) {
				return $item_data;
			}

			$bundled_item = $bundle->get_bundled_item( $cart_item['bundled_item_id'] );

			if ( ! $bundled_item ) {
				return $item_data;
			}

			$is_indented                  = false;
			$is_price_hidden              = false;
			$is_subtotal_hidden           = false;
			$is_thumbnail_hidden          = false;
			$is_visible                   = true;
			$is_last                      = self::$last_bundled_item_key === $cart_item['key'];
			$is_faked_parent_item         = self::$faked_parent_bundled_item_key === $cart_item['key'];
			$is_parent_visible            = false;
			$is_removable                 = false;
			$is_subtotal_aggregated       = false;
			$has_parent_with_cart_meta    = false;
			$has_parent_with_summary_meta = false;

			if ( ! $is_faked_parent_item ) {

				// Hide thumbnail?
				if ( false === $bundled_item->is_thumbnail_visible() ) {
					$is_thumbnail_hidden = true;
				}

				// Hide entire item?
				if ( false === $bundled_item->is_visible( 'cart' ) ) {
					$is_visible = false;
				}

				// Indent item?
				if ( WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'child_item_indent' ) ) {
					$is_indented                  = true;
					$has_parent_with_summary_meta = true;
				}

				// Parent item has meta?
				if ( WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'parent_cart_item_meta' ) ) {
					$has_parent_with_cart_meta    = true;
					$has_parent_with_summary_meta = true;
				}

				// Removable?
				if ( $bundled_item->is_optional() || $bundled_item->get_quantity( 'min' ) === 0 ) {
					$is_removable = true;
				}

				// Removes the parent?
				$is_parent_visible = WC_Product_Bundle::group_mode_has( $bundle->get_group_mode(), 'parent_item' );
				if ( ! $is_removable && ! $is_parent_visible ) {
					$is_removable = true;
				}
			}

			// Aggregate subtotals?
			if ( WC_Product_Bundle::group_mode_has( $container_item['data']->get_group_mode(), 'aggregated_subtotals' ) ) {
				$is_subtotal_aggregated = true;
			}

			// Hide price/subtotal?
			if ( false === $bundled_item->is_priced_individually() ) {

				$is_price_hidden    = WC_Product_Bundle::group_mode_has( $container_item['data']->get_group_mode(), 'aggregated_prices' ) && empty( $cart_item['data']->get_price() );
				$is_subtotal_hidden = WC_Product_Bundle::group_mode_has( $container_item['data']->get_group_mode(), 'aggregated_subtotals' ) && empty( $cart_item['line_subtotal'] );

			} elseif ( false === $bundled_item->is_price_visible( 'cart' ) ) {
				$is_price_hidden    = true;
				$is_subtotal_hidden = true;
			}

			if ( empty( $cart_item['data']->get_price() ) ) {
				$is_price_hidden = true;
			}

			if ( empty( $cart_item['line_subtotal'] ) ) {
				$is_subtotal_hidden = true;
			}

			$item_data['bundled_by']        = $cart_item['bundled_by'];
			$item_data['bundled_item_data'] = array(
				'bundle_id'              => $container_item['product_id'],
				'bundled_item_id'        => $cart_item['bundled_item_id'],
				'is_removable'           => $is_removable,
				'is_indented'            => $is_indented,
				'is_subtotal_aggregated' => $is_subtotal_aggregated,
				'is_parent_visible'      => $is_parent_visible,
				'is_last'                => $is_last,
				'is_price_hidden'        => $is_price_hidden,
				'is_subtotal_hidden'     => $is_subtotal_hidden,
				'is_thumbnail_hidden'    => $is_thumbnail_hidden,
				'is_hidden_in_cart'      => ! $is_visible || $has_parent_with_cart_meta,
				'is_hidden_in_summary'   => ! $is_visible || $has_parent_with_summary_meta,
			);
		}

		return $item_data;
	}

	/**
	 * Register product bundle schema into cart items endpoint.
	 *
	 * @return array Registered schema.
	 */
	public static function extend_cart_item_schema() {
		return array(
			'bundled_by'        => array(
				'description' => __( 'Cart item key of bundle that contains this item.', 'woocommerce-product-bundles' ),
				'type'        => array( 'string', 'null' ),
				'context'     => array( 'view', 'edit' ),
				'readonly'    => true,
			),
			'bundled_items'     => array(
				'description' => __( 'List of cart item keys grouped by this bundle.', 'woocommerce-product-bundles' ),
				'type'        => array( 'array', 'null' ),
				'context'     => array( 'view', 'edit' ),
				'readonly'    => true,
			),
			'bundle_data'       => array(
				'description' => __( 'Bundle data.', 'woocommerce-product-bundles' ),
				'type'        => array( 'object', 'null' ),
				'context'     => array( 'view', 'edit' ),
				'readonly'    => true,
			),
			'bundled_item_data' => array(
				'description' => __( 'ID of this bundled item.', 'woocommerce-product-bundles' ),
				'type'        => array( 'object', 'null' ),
				'context'     => array( 'view', 'edit' ),
				'readonly'    => true,
			),
		);
	}

	/**
	 * Aggregates bundle container item prices.
	 *
	 * @param array $item_data Cart item data.
	 * @param array $cart_item Cart item.
	 */
	private static function filter_container_cart_item_prices( &$item_data, $cart_item ) {

		if ( ! $cart_item['data']->is_type( 'bundle' ) ) {
			return;
		}

		if ( ! WC_Product_Bundle::group_mode_has( $cart_item['data']->get_group_mode(), 'aggregated_prices' ) ) {
			return;
		}

		/**
		 * 'woocommerce_store_api_before_bundle_aggregated_totals_calculation' hook.
		 *
		 * @since 6.20.0
		 *
		 * @param  array  $item_data  Cart item data.
		 * @param  array  $cart_item  Cart item.
		 */
		do_action( 'woocommerce_store_api_before_bundle_aggregated_totals_calculation', $item_data, $cart_item );

		$item_data['prices']->raw_prices['price']         = self::prepare_money_response( WC_PB()->display->get_container_cart_item_price_amount( $cart_item, 'price' ), wc_get_rounding_precision(), PHP_ROUND_HALF_UP );
		$item_data['prices']->raw_prices['regular_price'] = self::prepare_money_response( WC_PB()->display->get_container_cart_item_price_amount( $cart_item, 'regular_price' ), wc_get_rounding_precision(), PHP_ROUND_HALF_UP );
		$item_data['prices']->raw_prices['sale_price']    = self::prepare_money_response( WC_PB()->display->get_container_cart_item_price_amount( $cart_item, 'sale_price' ), wc_get_rounding_precision(), PHP_ROUND_HALF_UP );

		/**
		 * 'woocommerce_store_api_after_bundle_aggregated_totals_calculation' hook.
		 *
		 * @since 6.20.0
		 *
		 * @param  array  $item_data  Cart item data.
		 * @param  array  $cart_item  Cart item.
		 */
		do_action( 'woocommerce_store_api_after_bundle_aggregated_totals_calculation', $item_data, $cart_item );
	}

	/**
	 * Aggregates bundle container item subtotals.
	 *
	 * @param array $item_data Cart item data.
	 * @param array $cart_item Cart item.
	 */
	private static function filter_container_cart_item_totals( &$item_data, $cart_item ) {

		if ( ! $cart_item['data']->is_type( 'bundle' ) ) {
			return;
		}

		if ( ! WC_Product_Bundle::group_mode_has( $cart_item['data']->get_group_mode(), 'aggregated_subtotals' ) ) {
			return;
		}

		$decimals = isset( $item_data['totals']->currency_minor_unit ) ? $item_data['totals']->currency_minor_unit : wc_get_price_decimals();

		$item_data['totals']->line_total        = self::prepare_money_response( WC_PB()->display->get_container_cart_item_subtotal_amount( $cart_item, 'total' ), $decimals );
		$item_data['totals']->line_total_tax    = self::prepare_money_response( WC_PB()->display->get_container_cart_item_subtotal_amount( $cart_item, 'tax' ), $decimals );
		$item_data['totals']->line_subtotal     = self::prepare_money_response( WC_PB()->display->get_container_cart_item_subtotal_amount( $cart_item, 'subtotal' ), $decimals );
		$item_data['totals']->line_subtotal_tax = self::prepare_money_response( WC_PB()->display->get_container_cart_item_subtotal_amount( $cart_item, 'subtotal_tax' ), $decimals );
	}

	/**
	 * Adjust bundle container item quantity limits to keep max quantity limited by bundled item stock.
	 *
	 * @param array $item_data Cart item data.
	 * @param array $cart_item Cart item.
	 */
	private static function filter_container_cart_item_quantity_limits( &$item_data, $cart_item ) {

		if ( ! $cart_item['data']->is_type( 'bundle' ) ) {
			return;
		}

		$bundled_cart_items = wc_pb_get_bundled_cart_items( $cart_item );
		if ( $bundled_cart_items ) {

			foreach ( $bundled_cart_items as $bundled_cart_item_key => $bundled_cart_item ) {

				$bundled_item_id = $bundled_cart_item['bundled_item_id'];
				$bundled_item    = $cart_item['data']->get_bundled_item( $bundled_item_id );

				$min_bundled_item_quantity = $bundled_item->get_quantity( 'min' );
				$max_bundled_item_quantity = $bundled_item->get_quantity( 'max' );

				$bundle_quantity = $cart_item['quantity'];

				// Let's cache this now, as we'll need it later.
				WC_PB_Helpers::cache_set(
					'bundled_item_quantity_limits_' . $bundled_cart_item_key,
					array(
						'multiple_of' => $bundle_quantity,
						'minimum'     => $bundle_quantity * $min_bundled_item_quantity,
						'maximum'     => '' !== $max_bundled_item_quantity ? $bundle_quantity * $max_bundled_item_quantity : false,
					)
				);

				if ( $bundled_cart_item['data']->managing_stock() && ! $bundled_cart_item['data']->backorders_allowed() ) {

					$max_bundled_item_stock_quantity = $bundled_cart_item['data']->get_stock_quantity();

					if ( $max_bundled_item_stock_quantity > 0 && ! is_null( $max_bundled_item_stock_quantity ) ) {
						// Limit container max quantity based on child item stock quantity.
						$item_data['quantity_limits']->maximum = min( $item_data['quantity_limits']->maximum, floor( $max_bundled_item_stock_quantity / $bundled_cart_item['quantity'] ) * $bundle_quantity );
					}
				}
			}
		}
	}

	/**
	 * Filter container cart item permalink to support cart editing.
	 *
	 * @param array $item_data Cart item data.
	 * @param array $cart_item Cart item.
	 */
	private static function filter_container_cart_item_permalink( &$item_data, $cart_item ) {

		if ( wc_pb_is_bundle_container_cart_item( $cart_item ) ) {

			$bundle = $cart_item['data'];

			if ( ! $bundle->is_type( 'bundle' ) ) {
				return;
			}

			$item_data['permalink'] = add_query_arg( array( 'quantity' => $cart_item['quantity'] ), $bundle->get_permalink( $cart_item ) );

			if ( $bundle->is_editable_in_cart( $cart_item ) ) {

				$trimmed_short_description = '';

				if ( $item_data['short_description'] ) {
					$trimmed_short_description = '<p class="wc-block-components-product-metadata__description-text">' . wp_trim_words( $item_data['short_description'], 12 ) . '</p>';
				}

				$edit_link                      = esc_url( add_query_arg( array( 'update-bundle' => $cart_item['key'] ), $item_data['permalink'] ) );
				$item_data['short_description'] = '<p class="wc-block-cart-item__edit"><a class="wc-block-cart-item__edit-link" href="' . $edit_link . '">' . __( 'Edit item', 'woocommerce-product-bundles' ) . '</a></p>' . $trimmed_short_description;
			}
		}
	}

	/**
	 * Adjust bundled item quantity limits to account for min/max quantity settings and parent quantity.
	 *
	 * @param array $item_data Cart item data.
	 * @param array $cart_item Cart item.
	 */
	private static function filter_bundled_cart_item_quantity_limits( &$item_data, $cart_item ) {

		$bundled_cart_item_key        = $cart_item['key'];
		$bundled_item_quantity_limits = WC_PB_Helpers::cache_get( 'bundled_item_quantity_limits_' . $bundled_cart_item_key );

		if ( is_null( $bundled_item_quantity_limits ) ) {
			return;
		}

		$step = $item_data['quantity_limits']->multiple_of;
		$min  = $bundled_item_quantity_limits['minimum'];
		$max  = $bundled_item_quantity_limits['maximum'];

		$item_data['quantity_limits']->multiple_of = $step * $bundled_item_quantity_limits['multiple_of'];
		$item_data['quantity_limits']->minimum     = $min;

		if ( $max ) {
			// Limit child item max quantity.
			$item_data['quantity_limits']->maximum = min( $item_data['quantity_limits']->maximum, $max );
			$item_data['quantity_limits']->maximum = floor( $item_data['quantity_limits']->maximum / $step ) * $step;
		}
	}

	/**
	 * Convert monetary values from WooCommerce to string based integers, using
	 * the smallest unit of a currency.
	 *
	 * @param string|float $amount   The amount to format.
	 * @param int          $decimals The number of decimals.
	 * @param int          $rounding_mode The rounding mode.
	 * @return string
	 */
	private static function prepare_money_response( $amount, $decimals = 2, $rounding_mode = PHP_ROUND_HALF_UP ) {
		return woocommerce_store_api_get_formatter( 'money' )->format(
			$amount,
			array(
				'decimals'      => $decimals,
				'rounding_mode' => $rounding_mode,
			)
		);
	}

	/**
	 * Transform REST API data into Store API format.
	 *
	 * @param string $field Field name.
	 * @param mixed  $value Field value from REST API.
	 * @return mixed Transformed value for Store API.
	 */
	private static function transform_rest_to_store_data( $field, $value ) {
		switch ( $field ) {
			case 'bundle_price':
				// Store API expects prices in a different format.
				if ( is_array( $value ) && isset( $value['price'] ) ) {
					return woocommerce_store_api_get_formatter( 'currency' )->format(
						array(
							'price'         => array(
								'min' => array(
									'incl_tax' => self::prepare_money_response( $value['price']['min']['incl_tax'] ),
									'excl_tax' => self::prepare_money_response( $value['price']['min']['excl_tax'] ),
								),
								'max' => array(
									'incl_tax' => self::prepare_money_response( $value['price']['max']['incl_tax'] ),
									'excl_tax' => self::prepare_money_response( $value['price']['max']['excl_tax'] ),
								),
							),
							'regular_price' => array(
								'min' => array(
									'incl_tax' => self::prepare_money_response( $value['regular_price']['min']['incl_tax'] ),
									'excl_tax' => self::prepare_money_response( $value['regular_price']['min']['excl_tax'] ),
								),
								'max' => array(
									'incl_tax' => self::prepare_money_response( $value['regular_price']['max']['incl_tax'] ),
									'excl_tax' => self::prepare_money_response( $value['regular_price']['max']['excl_tax'] ),
								),
							),
						)
					);
				}
				break;

			case 'bundle_stock_status':
			case 'bundle_layout':
			case 'bundle_add_to_cart_form_location':
			case 'bundle_sold_individually_context':
			case 'bundle_item_grouping':
			case 'bundle_min_size': // these can be uninitialized, so return as string to preserve state.
			case 'bundle_max_size':
			case 'quantity_min':
			case 'quantity_max':
			case 'quantity_default':
				// Store API expects these as strings.
				return (string) $value;

			case 'bundle_virtual':
			case 'bundle_editable_in_cart':
			case 'override_title':
			case 'override_description':
			case 'optional':
			case 'hide_thumbnail':
			case 'override_variations':
			case 'override_default_variation_attributes':
				// Store API expects booleans.
				return (bool) $value;

			case 'bundle_stock_quantity':
			case 'menu_order':
				// Store API expects integers.
				return (int) $value;

			case 'bundled_items':
				// Transform each bundled item's data.
				if ( is_array( $value ) ) {
					return array_map(
						function ( $item ) {
							if ( ! is_array( $item ) ) {
								return $item;
							}
							$transformed = array();
							foreach ( $item as $key => $val ) {
								$transformed[ $key ] = self::transform_rest_to_store_data( $key, $val );
							}
							return $transformed;
						},
						$value
					);
				}
				return $value;
		}

		return $value;
	}

	/**
	 * Extend Store API's product endpoint to include information about the bundle product.
	 *
	 * Reuses REST API's methods, but filters the properties using self::$product_fields.
	 *
	 * @param WC_Product $product Product to extend the API data with.
	 *
	 * @return array
	 */
	public static function extend_product_item_data( WC_Product $product ): array {
		$bundle_data = array();

		if ( ! $product->is_type( 'bundle' ) ) {
			return $bundle_data;
		}

		foreach ( self::$product_fields as $field => $include_field ) {
			// if the field is bundled_items array, only include allowed data properties.
			if ( 'bundled_items' === $field ) {
				$all_bundled_items = WC_PB_REST_API::get_product_field( $field, $product );

				foreach ( $all_bundled_items as $item_properties ) {
					$filtered_properties = array_filter(
						$item_properties,
						function ( $item_property ) use ( $field ) {
							return isset( self::$product_fields[ $field ][ $item_property ] ) && self::$product_fields[ $field ][ $item_property ];
						},
						ARRAY_FILTER_USE_KEY
					);

					$bundle_data[ $field ][] = self::transform_rest_to_store_data( $field, $filtered_properties );
				}
			} elseif ( $include_field ) {
				$value                 = WC_PB_REST_API::get_product_field( $field, $product );
				$bundle_data[ $field ] = self::transform_rest_to_store_data( $field, $value );
			}
		}

		return $bundle_data;
	}

	/**
	 * Extend Store API product endpoint's schema to include information about the bundle product.
	 *
	 * @return array
	 */
	public static function extend_product_item_schema(): array {
		if ( isset( self::$product_item_schema_cache ) ) {
			return self::$product_item_schema_cache;
		}

		$rest_api_schema = WC_PB_REST_API::get_extended_product_schema();
		// REST API uses `id` and `bundled_item_id` fairly liberally, but only the latter is accepted by the Store API.
		if ( isset( $rest_api_schema['bundled_items']['items']['properties']['id'] ) ) {
			$temp = $rest_api_schema['bundled_items']['items']['properties']['id'];

			unset( $rest_api_schema['bundled_items']['items']['properties']['id'] );
			$rest_api_schema['bundled_items']['items']['properties']['bundled_item_id'] = $temp;
		}

		$store_api_schema = array_filter(
			$rest_api_schema,
			function ( $field ) {
				return isset( self::$product_fields[ $field ] ) && self::$product_fields[ $field ];
			},
			ARRAY_FILTER_USE_KEY
		);

		// Make sure to respect the bundled_items.
		$bundled_items_properties                                 = $rest_api_schema['bundled_items']['items']['properties'];
		$store_api_schema['bundled_items']['items']['properties'] = array_filter(
			$bundled_items_properties,
			function ( $item_property ) {
				return isset( self::$product_fields['bundled_items'][ $item_property ] ) && self::$product_fields['bundled_items'][ $item_property ];
			},
			ARRAY_FILTER_USE_KEY
		);

		self::$product_item_schema_cache = $store_api_schema;
		return self::$product_item_schema_cache;
	}

	/*
	|--------------------------------------------------------------------------
	| Callbacks.
	|--------------------------------------------------------------------------
	*/

	/**
	 * Filter store API responses to:
	 *
	 * - aggregate bundle container prices/subtotals;
	 * - keep min/max/step quantity fields in sync.
	 *
	 * @param  WP_REST_Response $response The response data.
	 * @param  WP_REST_Server   $server The server instance.
	 * @param  WP_REST_Request  $request The request instance.
	 * @return WP_REST_Response
	 */
	public static function filter_cart_item_data( $response, $server, $request ) {

		if ( is_wp_error( $response ) ) {
			return $response;
		}

		if ( strpos( $request->get_route(), 'wc/store' ) === false ) {
			return $response;
		}

		$data = $response->get_data();

		if ( empty( $data['items'] ) ) {
			return $response;
		}

		if ( ! WC()->cart ) {
			return $response;
		}

		$cart = WC()->cart->get_cart();

		foreach ( $data['items'] as &$item_data ) {

			$cart_item_key = $item_data['key'];
			$cart_item     = isset( $cart[ $cart_item_key ] ) ? $cart[ $cart_item_key ] : null;

			if ( is_null( $cart_item ) ) {
				continue;
			}

			/**
			 * StoreAPI returns the following fields as
			 * - object (/wc/store/v1/cart)
			 * - array (/wc/store/v1/cart/extensions)
			 *
			 * Casting them to objects, to avoid PHP8+ fatal errors.
			 *
			 * @see https://github.com/woocommerce/woocommerce-product-bundles/issues/1096
			 * @see https://github.com/woocommerce/woocommerce-blocks/issues/7275
			 */
			$item_data['quantity_limits'] = (object) $item_data['quantity_limits'];
			$item_data['prices']          = (object) $item_data['prices'];
			$item_data['totals']          = (object) $item_data['totals'];
			$item_data['extensions']      = (object) $item_data['extensions'];

			if ( wc_pb_is_bundle_container_cart_item( $cart_item ) ) {

				self::filter_container_cart_item_prices( $item_data, $cart_item );
				self::filter_container_cart_item_totals( $item_data, $cart_item );
				self::filter_container_cart_item_quantity_limits( $item_data, $cart_item );
				self::filter_container_cart_item_permalink( $item_data, $cart_item );

			} elseif ( wc_pb_is_bundled_cart_item( $cart_item ) ) {

				self::filter_bundled_cart_item_quantity_limits( $item_data, $cart_item );
			}
		}

		$response->set_data( $data );

		return $response;
	}

	/**
	 * Validate bundle in Store API context.
	 *
	 * @throws RouteException If there is a validation error.
	 *
	 * @param  array $bundle Bundle data.
	 * @param  array $cart_item Cart item data.
	 * @return void
	 */
	public static function validate_cart_item( $bundle, $cart_item ) {

		if ( wc_pb_is_bundle_container_cart_item( $cart_item ) ) {
			try {
				WC_PB()->cart->validate_bundle_configuration( $cart_item['data'], $cart_item['quantity'], $cart_item['stamp'], 'cart' );
			} catch ( Exception $e ) {
				$notice = $e->getMessage();
				throw new RouteException( 'woocommerce_store_api_invalid_bundle_configuration', esc_html( $notice ) );
			}
		}
	}

	/**
	 * Prevents access to the checkout block if a bundle in the cart is misconfigured.
	 *
	 * @throws RouteException If there is a validation error.
	 * @param  WC_Order $order  The order object.
	 * @return void
	 */
	public static function validate_draft_order( $order ) { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.Found

		foreach ( WC()->cart->cart_contents as $cart_item_key => $cart_item ) {
			self::validate_cart_item( $cart_item['data'], $cart_item );
		}
	}

	/**
	 * Prevent removal of mandatory bundled items with a visible parent.
	 *
	 * @throws RouteException If the cart item is mandatory.
	 *
	 * @param  string  $cart_item_key The cart item key.
	 * @param  WC_Cart $cart The cart object.
	 * @return void
	 */
	public static function validate_mandatory_bundled_cart_item_removal( $cart_item_key, $cart ) { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.FoundAfterLastUsed

		if ( ! WC_PB_Core_Compatibility::is_store_api_request( 'cart/remove-item' ) ) {
			return;
		}

		$notice = __( 'This product is a mandatory part of a bundle and cannot be removed.', 'woocommerce-product-bundles' );
		throw new RouteException( 'woocommerce_store_api_mandatory_bundled_item', esc_html( $notice ) );
	}
}
