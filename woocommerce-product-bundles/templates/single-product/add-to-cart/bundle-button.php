<?php
/**
 * Bundle add-to-cart button template
 *
 * Override this template by copying it to 'yourtheme/woocommerce/single-product/add-to-cart/bundle-button.php'.
 *
 * On occasion, this template file may need to be updated and you (the theme developer) will need to copy the new files to your theme to maintain compatibility.
 * We try to do this as little as possible, but it does happen.
 * When this occurs the version of the template file will be bumped and the readme will list any important changes.
 *
 * @version 6.21.0
 * @package WooCommerce Product Bundles
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

global $product;

?>
<button type="submit" name="add-to-cart" value="<?php echo esc_attr( $product->get_id() ); ?>"
		class="<?php echo isset( $button_class ) ? esc_attr( $button_class ) : 'single_add_to_cart_button bundle_add_to_cart_button button alt'; ?>"><?php echo esc_html( $product->single_add_to_cart_text() ); ?></button>
