<?php
/**
 * Custom content-single-product.php
 */
defined( 'ABSPATH' ) || exit;

global $product;

do_action( 'woocommerce_before_single_product' );

if ( post_password_required() ) {
    echo get_the_password_form();
    return;
}
?>

<div id="product-<?php the_ID(); ?>" <?php wc_product_class(); ?> >
    <div class="product-columns-wrapper fade-in">
        <!-- Left Column: Custom Image Gallery -->
        <div class="product-image-gallery">
            <?php
            // Get featured image or WooCommerce placeholder
            $featured_image = get_post_thumbnail_id() ? wp_get_attachment_url( get_post_thumbnail_id() ) : wc_placeholder_img_src();
            echo '<div class="main-image"><a data-fancybox="gallery" href="' . esc_url( $featured_image ) . '"><img src="' . esc_url( $featured_image ) . '" class="product-image" /></a></div>';

            // Display gallery images
            $attachment_ids = $product->get_gallery_image_ids();

            if ( ! empty( $attachment_ids ) ) {
                echo '<div class="gallery-grid">';
                
                // Loop to structure images in 50/50 and 100% layout
                foreach ( $attachment_ids as $index => $attachment_id ) {
                    $image_url = wp_get_attachment_url( $attachment_id );

                    // Determine layout for the current image
                    if ( $index % 4 === 2 ) { // Full-width image
                        echo '<div class="full-width"><a data-fancybox="gallery" href="' . esc_url( $image_url ) . '"><img src="' . esc_url( $image_url ) . '" class="product-image" /></a></div>';
                    } else { // 50/50 image
                        echo '<div class="half-width"><a data-fancybox="gallery" href="' . esc_url( $image_url ) . '"><img src="' . esc_url( $image_url ) . '" class="product-image" /></a></div>';
                    }
                }

                echo '</div>'; // End gallery-grid
            }
            ?>


            <?php
            $attachment_ids = $product->get_gallery_image_ids();
            $featured_image = get_post_thumbnail_id() ? wp_get_attachment_url( get_post_thumbnail_id() ) : '';

            if ($featured_image || !empty($attachment_ids)): ?>
                <div class="mobile-slider">
                    <div class="slides">
                        <?php if ($featured_image): ?>
                            <img src="<?php echo esc_url($featured_image); ?>" alt="Huvudbild">
                        <?php endif; ?>

                        <?php foreach ($attachment_ids as $attachment_id): ?>
                            <img src="<?php echo esc_url(wp_get_attachment_url($attachment_id)); ?>" alt="Galleri-bild">
                        <?php endforeach; ?>
                    </div>
                    <!-- Slider-pilar -->
                    <button class="slider-nav prev">&larr;</button>
                    <button class="slider-nav next">&rarr;</button>
                </div>
            <?php endif; ?>



        </div>

        <!-- Right Column: Product Summary -->
        <div class="product-summary">
            <?php do_action( 'woocommerce_single_product_summary' ); ?>
        </div>
    </div>
</div>

    <!-- TAAAABS  -->
    <div class="custom-tabs-wrapper fade-in">
    <h2 class="tabs-main-title">Detaljer</h2>
    <?php
    // Kontrollera innehåll för varje tab
    $specifikation_innehall = true; // "Fullständig specifikation" är alltid synlig
    $om_produkten_innehall = get_field('rubrik_sektion_1') || get_field('text_sektion_1') || get_field('bild_sektion_1');
    $geometri_innehall = get_field('specifikationer_bild');
    $fordelar_innehall = get_field('rubrik_sektion_2') || get_field('text_sektion_2') || get_field('bild_sektion_2');
    ?>
    <!-- Tabbar -->
    <div class="custom-tabs">
        <?php if ($specifikation_innehall): ?>
            <button class="tab-button active" data-tab="specifikation">Fullständig specifikation</button>
        <?php endif; ?>
        <?php if ($om_produkten_innehall): ?>
            <button class="tab-button" data-tab="om-produkten">Om produkten</button>
        <?php endif; ?>
        <?php if ($geometri_innehall): ?>
            <button class="tab-button" data-tab="geometri">Geometri</button>
        <?php endif; ?>
        <?php if ($fordelar_innehall): ?>
            <button class="tab-button" data-tab="fordelar">Upplev fördelarna</button>
        <?php endif; ?>
    </div>

    <!-- Tab Innehåll -->
    <?php if ($specifikation_innehall): ?>
        <div class="tab-content active" id="specifikation">
            <div id="spec-row" class="specifikation-row" style="background-color:#fff; width: 100%;">
                <div id="user-selected-row" class="text short"></div> 
            </div> 
        </div>
    <?php endif; ?>

    <?php if ($om_produkten_innehall): ?>
        <div class="tab-content" id="om-produkten">
            <?php
            $rubrik_sektion_1 = get_field('rubrik_sektion_1');
            $text_sektion_1 = get_field('text_sektion_1');
            $bild_sektion_1 = get_field('bild_sektion_1');
            ?>
            <div class="tab-section">
                <div class="tab-text">
                    <?php if ($rubrik_sektion_1): ?>
                        <h2><?php echo esc_html($rubrik_sektion_1); ?></h2>
                    <?php endif; ?>
                    <?php if ($text_sektion_1): ?>
                        <div class="wysiwyg-content">
                            <?php echo wp_kses_post($text_sektion_1); ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if ($bild_sektion_1): ?>
                    <div class="tab-image">
                        <img src="<?php echo esc_url($bild_sektion_1); ?>" alt="<?php echo esc_attr($rubrik_sektion_1); ?>" />
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($geometri_innehall): ?>
        <div class="tab-content" id="geometri">
            <?php
            $specifikationer_bild = get_field('specifikationer_bild');
            ?>
            <div class="tab-section">
                <?php if ($specifikationer_bild): ?>
                    <div class="tab-image">
                        <img src="<?php echo esc_url($specifikationer_bild); ?>" alt="Geometri" />
                    </div>
                <?php else: ?>
                    <p>Ingen geometri bild tillgänglig.</p>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($fordelar_innehall): ?>
        <div class="tab-content" id="fordelar">
            <?php
            $rubrik_sektion_2 = get_field('rubrik_sektion_2');
            $text_sektion_2 = get_field('text_sektion_2');
            $bild_sektion_2 = get_field('bild_sektion_2');
            ?>
            <div class="tab-section">
                <div class="tab-text">
                    <?php if ($rubrik_sektion_2): ?>
                        <h2><?php echo esc_html($rubrik_sektion_2); ?></h2>
                    <?php endif; ?>
                    <?php if ($text_sektion_2): ?>
                        <div class="wysiwyg-content">
                            <?php echo wp_kses_post($text_sektion_2); ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if ($bild_sektion_2): ?>
                    <div class="tab-image">
                        <img src="<?php echo esc_url($bild_sektion_2); ?>" alt="<?php echo esc_attr($rubrik_sektion_2); ?>" />
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>



<!---- HOTSPOTS ------> 

<?php
// Hämta ACF-fält för båda bilderna
$hotspot_bild_1 = get_field('hotspot_bild_1');
$hotspot_punkter_1 = get_field('hotspot_punkter_1');

$hotspot_bild_2 = get_field('hotspot_bild_2');
$hotspot_punkter_2 = get_field('hotspot_punkter_2');

// Kontrollera att minst en bild finns
if ($hotspot_bild_1 || $hotspot_bild_2): ?>
    <div class="hotspot-section-container fade-in">
        <?php if ($hotspot_bild_1): ?>
            <div class="hotspot-container half-width">
                <img src="<?php echo esc_url($hotspot_bild_1); ?>" alt="Hotspot Bild 1" style="width: 100%; height: auto;">
                <?php if ($hotspot_punkter_1): ?>
                    <?php foreach ($hotspot_punkter_1 as $punkt): ?>
                        <div class="hotspot" style="left: <?php echo esc_attr($punkt['hotspot_x']); ?>%; top: <?php echo esc_attr($punkt['hotspot_y']); ?>%;">
                            <div class="hotspot-dot"></div>
                            <div class="hotspot-info"><?php echo esc_html($punkt['hotspot_info']); ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if ($hotspot_bild_2): ?>
            <div class="hotspot-container half-width">
                <img src="<?php echo esc_url($hotspot_bild_2); ?>" alt="Hotspot Bild 2" style="width: 100%; height: auto;">
                <?php if ($hotspot_punkter_2): ?>
                    <?php foreach ($hotspot_punkter_2 as $punkt): ?>
                        <div class="hotspot" style="left: <?php echo esc_attr($punkt['hotspot_x']); ?>%; top: <?php echo esc_attr($punkt['hotspot_y']); ?>%;">
                            <div class="hotspot-dot"></div>
                            <div class="hotspot-info"><?php echo esc_html($punkt['hotspot_info']); ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>



<!---- Produktbild slider ----->

<?php
// Hämta repeaterfältet för slidern
$bilder = get_field('produkt_bild_slider');

if ($bilder): ?>
    <div class="produkt-slider-container fade-in">
        <div class="produkt-slider">
            <?php foreach ($bilder as $index => $bild): ?>
                <div class="produkt-slide <?php echo $index === 0 ? 'active' : ''; ?>">
                    <img src="<?php echo esc_url($bild['slider_bild']); ?>" alt="Produktbild <?php echo $index + 1; ?>">
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Navigationspilar -->
        <button class="slider-nav prev">&larr;</button>
        <button class="slider-nav next">&rarr;</button>

        <!-- Bildindikator -->
        <div class="slider-indicator">
            <div class="indicator-progress"></div>
        </div>
    </div>
<?php endif; ?>


<!---- Relaterade produkter ----->
<div class="custom-tabs-wrapper related-products-section ">
    <h2 class="tabs-main-title">Utforska fler val</h2>

    <!-- Tabbar -->
    <div class="custom-tabs">
        <button class="tab-button active" data-tab="populart-tillval">Populära cyklar</button>
        <button class="tab-button" data-tab="senast-visade">Tillval</button>
    </div>

    <!-- Innehåll för "Populära tillval" -->
    <div class="tab-content active" id="populart-tillval">
        <?php
        // Hämta relaterade produkter från kategorin "Tillval"
        $args = array(
            'post_type'      => 'product',
            'posts_per_page' => 3, // Antal produkter
            'tax_query'      => array(
                array(
                    'taxonomy' => 'product_cat',
                    'field'    => 'slug',
                    'terms'    => 'cyklar', // Kategorinamn
                ),
            ),
        );

        $related_query = new WP_Query($args);

        if ($related_query->have_posts()) : ?>
            <ul class="products columns-4">
                <?php while ($related_query->have_posts()) : $related_query->the_post(); ?>
                    <?php wc_get_template_part('content', 'product'); ?>
                <?php endwhile; ?>
            </ul>
        <?php else: ?>
            <p>Inga populära tillval hittades.</p>
        <?php endif; wp_reset_postdata(); ?>
    </div>

    <!-- Innehåll för "Senast visade produkter" -->
    <!-- Innehåll för "Tillval" -->
    <div class="tab-content" id="senast-visade">
    <?php
    // Hämta produkter från kategorin "dack", inklusive dolda produkter
    $args = array(
        'post_type'      => 'product',
        'posts_per_page' => 4, // Antal produkter
        'orderby'        => 'rand', // Slumpmässig ordning
        'tax_query'      => array(
            'relation' => 'AND',
            array(
                'taxonomy' => 'product_cat',
                'field'    => 'slug',
                'terms'    => 'dack', // Kategorinamn
            ),
            array(
                'taxonomy' => 'product_visibility',
                'field'    => 'slug',
                'terms'    => array('exclude-from-catalog'), // Undanta katalogexkludering
                'operator' => 'NOT IN', // Inkludera dolda produkter
            ),
        ),
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key'     => '_visibility',
                'value'   => 'hidden',
                'compare' => '=', // Inkludera produkter markerade som dolda
            ),
            array(
                'key'     => '_visibility',
                'compare' => 'NOT EXISTS', // Inkludera produkter utan synlighetsvärde
            ),
        ),
    );

    $related_query = new WP_Query($args);

    if ($related_query->have_posts()) : ?>
        <ul class="products columns-4">
            <?php while ($related_query->have_posts()) : $related_query->the_post(); ?>
                <?php wc_get_template_part('content', 'product'); ?>
            <?php endwhile; ?>
        </ul>
    <?php else: ?>
        <p>Inga produkter hittades.</p>
    <?php endif; wp_reset_postdata(); ?>
</div>

</div>



<style>
.product-columns-wrapper {
    display: flex;
    flex-wrap: nowrap;
    gap: 5px;
}

.product-image-gallery {
    flex: 0 0 55%;
    display: flex;
    flex-direction: column;
    padding: 20px; /* Added padding */
    box-sizing: border-box; /* Ensure padding does not affect width */
    background-color: #f9f9f9;
    max-height: 1000px;
    overflow-y: scroll;
    scrollbar-width: none; /* Hide scrollbar for Firefox */
    -ms-overflow-style: none; /* Hide scrollbar for IE/Edge */
}

.product-image-gallery::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari, and Opera */
}

.main-image img {
    width: 100%;
    border-radius: 10px;
}

.gallery-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.gallery-grid .half-width {
    flex: 1 1 calc(50% - 10px);
}

.gallery-grid .full-width {
    flex: 1 1 100%;
}

.gallery-grid img {
    width: 100%;
    border-radius: 10px;
    height: 450px;
    object-fit: cover;
}

.product-summary {
    flex: 0 0 45%;
    display: flex;
    flex-direction: column;
    padding: 4% 2%;
    box-sizing: border-box;
    max-height: none;
    overflow-y: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.product_meta, .woocommerce div.product form.cart div.quantity, .yith-wapo-block .yith-wapo-addon.wapo-toggle .wapo-addon-title:before,.yith-wapo-block .yith-wapo-addon .title-image {
    display: none !important;
}
.woocommerce div.product form.cart .button {
    vertical-align: middle;
    float: left;
    width: calc(100% - 10px);
    background-color: #000;
    padding: 25px 0px;
    font-family: var(--FontFamilySecond) !important;
    font-weight: 500;
    margin-top:20px;
    font-size: 1.5rem;
    border-radius: 50px;
}
.woocommerce div.product .product_title{
    margin-bottom: 0px;
    font-size: 2.5rem;
}
.woocommerce-product-details__short-description p {
    font-size: 16px;
    margin: 20px 0px 0px;
    color: #5b5b5b;
}

.yith-wapo-block .yith-wapo-addon.wapo-toggle .wapo-addon-title, .cykel-modeller h3{
    font-size: 2rem !important;
    margin-bottom: 15px;
    margin-top: 15px;
}
.yith-wapo-block .yith-wapo-addon.wapo-toggle .options.default-open {
    display: flex;
    flex-wrap: wrap;
}
tr.wapo-total-options, tr.wapo-product-price{
    display: none;
}

/* Styling modeller */ 

.cykel-modeller {
    margin-top: 20px;
}

.cykel-modeller h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #333;
}

.cykel-modeller-list {
    list-style: none;
    padding: 0;
    display: flex;
    gap: 10px;
}

.cykel-modell-item {
    background-color: transparent;
    padding: 10px 30px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 1.5rem;
    line-height: 2;
}

.cykel-modell-item a {
    text-decoration: none;
    color: #000;
    font-family: 'Arial';
}

.cykel-modell-item.current-model {
    background-color: transparent;
    color: #fff;
    font-weight: bold;
    pointer-events: none;
    border: 2px solid #000;
}



/* Slider Container */
.produkt-slider-container {
    position: relative;
    width: 100%;
    max-width: 100vw;
    margin: 0 auto;
    overflow: hidden;
    background: #000;
}

/* Slider */
.produkt-slider {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.produkt-slide {
    min-width: 100%;
    box-sizing: border-box;
    position: relative;
}

.produkt-slide img {
    width: 100%;
    display: block;
    max-height: 880px;
    object-fit: cover;
}

/* Navigationspilar - Placeras i nedre högra hörnet */
.slider-nav {
    position: absolute;
    bottom: 20px; /* Höjd för att matcha indikatorn */
    right: 20px; /* Höger hörn */
    background-color: transparent;
    color: #fff;
    border: none;
    border: 2px solid #fff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background 0.3s ease;
    margin-left: 10px; /* Avstånd mellan pilarna */
}

.slider-nav:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

/* Justera pilar för att ligga jämte varandra */
.slider-nav.prev {
    right: 70px; /* Flytta vänsterpil bredvid högerpil */
}


/* Bildindikator */
.slider-indicator {
    position: absolute;
    bottom: 40px;
    left: 40px;
    width: 120px;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.3);
    overflow: hidden;
    border-radius: 2px;
}

.indicator-progress {
    width: 0;
    height: 100%;
    background-color: #fff;
    transition: width 0.5s ease-in-out;
}


/* Responsiv fix */ 


/* Grundläggande responsiv styling */
/* Grundläggande styling för desktop */
.product-columns-wrapper {
    display: flex;
    gap: 0px;
}

.product-image-gallery {
    flex: 0 0 55%; /* Galleri 60% */
}

.product-summary {
    flex: 0 0 45%; /* Summary 40% */
}

/* Anpassningar för mobil */
@media (max-width: 768px) {
    .product-columns-wrapper {
        flex-direction: column; /* Lägg under varandra */
        gap: 0;
    }

    .product-image-gallery,
    .product-summary {
        flex: 1 1 100%; /* Full bredd */
    }

    /* Dölj det vanliga galleriet i mobil */
    .gallery-grid,
    .main-image {
        display: none;
    }

    /* Mobilslidern syns bara i mobil */
    .mobile-slider {
        display: block;
        position: relative;
        overflow: hidden;
        width: 100%;
    }

    .mobile-slider img {
        width: 100%;
        display: block;
        border-radius: 10px;
        object-fit: contain;
    }

    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        border: none;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        z-index: 10;
    }

    .slider-nav.prev {
        left: 10px;
    }

    .slider-nav.next {
        right: 10px;
    }
    h2.tabs-main-title{
        font-size: 3rem;
    }
    .woocommerce .products ul, .woocommerce ul.products{
        flex-direction: column;
    }
    .woocommerce ul.products li.product, .woocommerce-page ul.products li.product{
        width: calc(100% - 20px) !important;
    }
}

@media (min-width: 769px) {
    .mobile-slider {
        display: none; /* Dölj slidern i desktop */
    }
}

/* Sidledes scroll för mobil */
@media (max-width: 768px) {
    .custom-tabs {
        display: flex;
        overflow-x: auto; /* Möjliggör sid-scroll */
        white-space: nowrap; /* Håll tabbarna på en rad */
        -webkit-overflow-scrolling: touch; /* Smidig scroll för iOS */
        gap: 10px; /* Litet mellanrum mellan tabbarna */
        padding-bottom: 5px; /* Extra spacing under tabbarna */
    }

    .custom-tabs::-webkit-scrollbar {
        display: none; /* Dölj scrollbar i Chrome/Safari */
    }

    .tab-button {
        flex: 0 0 45%;
        background-color: transparent;
        border: 0px;
        border-bottom: 2px solid rgb(181, 181, 181);
        padding: 7px 0px;
        font-size: 11px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: left;
        font-family: 'Arial';
        white-space: normal;
    }

    .tab-button.active {
        border-bottom: 2px solid #000; /* Aktiv tabb får svart markering */
        font-weight: bold;
    }
    .product-summary{
        max-height: none !important;
    }
}

/* Styling för desktop */
@media (min-width: 769px) {
    .tab-button {
        width: 25%; /* Fyra tabbar delar på bredden i desktop */
        text-align: left;
    }
}




</style>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const addons = document.querySelectorAll('.yith-wapo-addon');

    addons.forEach(addon => {
        const options = addon.querySelectorAll('.yith-wapo-option');
        if (options.length === 1) {
            addon.style.display = 'none';
        }
    });
});


document.addEventListener('DOMContentLoaded', function () {
    // Loopar igenom alla tabbsektioner
    document.querySelectorAll('.custom-tabs-wrapper').forEach((wrapper) => {
        const tabs = wrapper.querySelectorAll('.tab-button');
        const contents = wrapper.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Ta bort "active" endast inom den aktuella tabbgruppen
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));

                // Lägg till "active" på den valda tabben och innehållet
                tab.classList.add('active');
                const target = wrapper.querySelector(`#${tab.getAttribute('data-tab')}`);
                if (target) {
                    target.classList.add('active');
                }
            });
        });
    });
});



// get all the checkbox elements
const checkboxes = document.querySelectorAll('input[type="checkbox"]');

// function to update the checkboxes display
function updateCheckboxDisplay() {
  // clear the existing contents of the "geometri-row" div
  const geometriRow = document.querySelector('#user-selected-row');
  geometriRow.innerHTML = '';
  let parentTitle = ''; // declare parentTitle here
  let parentImage = ''; // declare parentImage here
  // loop through all the checkboxes and check which ones are checked
  let ourCounter = 0;
  
  let totalCount = jQuery( ".wapo-addon-title" ).length;
  checkboxes.forEach((checkbox,index) => {
    if (checkbox.checked) {
      // get the relevant details for the checked checkbox
      const label = checkbox.nextElementSibling;
      const productName = label.querySelector('div').textContent.trim();
      const productId = checkbox.value;
	  
	  for (let i = 0; i < totalCount; i++) {
		  if(i==ourCounter){
			  parentTitle = jQuery( ".wapo-addon-title" ).eq(i).text().toUpperCase();
			  parentImage = jQuery( ".title-image img" ).eq(i).attr("src");
		  }
		}
	  
		// <div class="product-id">Product ID: ${productId}</div>

      // create a div for the checked checkbox and its details
      const checkboxDiv = document.createElement('div');
      checkboxDiv.classList.add('checkbox-details');
	  //checkboxDiv.innerHTML = `<div class="parent-title">${parentTitle}</div><div class="product-name">${productName}</div><div class="product-id">Product ID: ${productId}</div>`;
	  checkboxDiv.innerHTML = `  
	  <div class="col-2-table">
      <div class="parent-title">${parentTitle}</div>
	  <div class="product-name">${productName}</div> 
	  </div> 
`;	
		
// append the div to the "geometri-row" div
      geometriRow.appendChild(checkboxDiv);
	  
	ourCounter++;
    }
  });

  // if no checkboxes are checked, create a message saying so
  if (geometriRow.children.length === 0) {
    const noSelectionDiv = document.createElement('div');
    noSelectionDiv.classList.add('no-selection');
    noSelectionDiv.textContent = 'No checkboxes selected';
    geometriRow.appendChild(noSelectionDiv);
  }
}

// update the checkboxes display on page load
document.addEventListener('DOMContentLoaded', updateCheckboxDisplay);

// add an event listener to each checkbox element
checkboxes.forEach(checkbox => {
  checkbox.addEventListener('click', function() {
    setTimeout(function() {
      // update the checkboxes display
      updateCheckboxDisplay();
    }, 500);
  });
});


document.addEventListener('DOMContentLoaded', function () {
    const slides = document.querySelectorAll('.produkt-slide');
    const prevButton = document.querySelector('.slider-nav.prev');
    const nextButton = document.querySelector('.slider-nav.next');
    const progressIndicator = document.querySelector('.indicator-progress');

    let currentIndex = 0;

    function updateSlider() {
        const slider = document.querySelector('.produkt-slider');
        slider.style.transform = `translateX(-${currentIndex * 100}%)`;
        progressIndicator.style.width = `${((currentIndex + 1) / slides.length) * 100}%`;
    }

    prevButton.addEventListener('click', () => {
        currentIndex = (currentIndex === 0) ? slides.length - 1 : currentIndex - 1;
        updateSlider();
    });

    nextButton.addEventListener('click', () => {
        currentIndex = (currentIndex === slides.length - 1) ? 0 : currentIndex + 1;
        updateSlider();
    });

    // Initiera indikator
    updateSlider();
});



document.addEventListener('DOMContentLoaded', () => {
    const sections = document.querySelectorAll('.fade-in');

    // Funktion som kollar om en sektion är synlig
    const isVisible = (element) => {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= window.innerHeight && rect.bottom >= 0
        );
    };

    const handleScroll = () => {
        sections.forEach((section) => {
            if (isVisible(section)) {
                section.classList.add('show');
            } else {
                section.classList.remove('show'); // Ta bort show för att animera igen
            }
        });
    };

    // Kör funktionen på scroll och vid laddning
    window.addEventListener('scroll', handleScroll);
    handleScroll(); // Kör en gång vid sidladdning
});


// BILDGALLERI PRODUKTBILDER 

document.addEventListener('DOMContentLoaded', function () {
    const slider = document.querySelector('.mobile-slider .slides');
    const prevButton = document.querySelector('.slider-nav.prev');
    const nextButton = document.querySelector('.slider-nav.next');
    let currentIndex = 0;
    const images = document.querySelectorAll('.mobile-slider .slides img');

    // Visa endast en bild i taget
    function updateSlider() {
        slider.style.transform = `translateX(-${currentIndex * 100}%)`;
    }

    // Föregående bild
    prevButton.addEventListener('click', () => {
        currentIndex = currentIndex > 0 ? currentIndex - 1 : images.length - 1;
        updateSlider();
    });

    // Nästa bild
    nextButton.addEventListener('click', () => {
        currentIndex = (currentIndex + 1) % images.length;
        updateSlider();
    });

    // Initiera slidern
    slider.style.display = 'flex';
    slider.style.transition = 'transform 0.5s ease-in-out';
    images.forEach(img => {
        img.style.flex = '0 0 100%';
        img.style.maxWidth = '100%';
    });
    updateSlider();
});



// Bildgalleri längre ned 


document.addEventListener('DOMContentLoaded', function () {
    const sliderContainer = document.querySelector('.produkt-slider-container');
    const slides = sliderContainer.querySelectorAll('.produkt-slide');
    const prevButton = sliderContainer.querySelector('.slider-nav.prev');
    const nextButton = sliderContainer.querySelector('.slider-nav.next');
    const progressIndicator = sliderContainer.querySelector('.indicator-progress');
    let currentIndex = 0;
    const totalSlides = slides.length;

    // Funktion för att uppdatera slidern
    function updateSlider() {
        const slider = sliderContainer.querySelector('.produkt-slider');
        slider.style.transform = `translateX(-${currentIndex * 100}%)`;

        // Uppdatera indikatorns bredd
        const progressWidth = ((currentIndex + 1) / totalSlides) * 100;
        progressIndicator.style.width = `${progressWidth}%`;

        // Uppdatera aktiva klass
        slides.forEach((slide, index) => {
            slide.classList.toggle('active', index === currentIndex);
        });
    }

    // Hantera "Föregående"-knappen
    prevButton.addEventListener('click', () => {
        currentIndex = (currentIndex === 0) ? totalSlides - 1 : currentIndex - 1;
        updateSlider();
    });

    // Hantera "Nästa"-knappen
    nextButton.addEventListener('click', () => {
        currentIndex = (currentIndex + 1) % totalSlides;
        updateSlider();
    });

    // Initialisera slidern
    updateSlider();
});



</script>
