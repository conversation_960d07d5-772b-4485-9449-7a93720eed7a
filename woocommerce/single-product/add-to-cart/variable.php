<?php
defined('ABSPATH') || exit;

global $product;

$attribute_keys   = array_keys($attributes);
$available_vars   = $product->get_available_variations();
$variations_json  = wp_json_encode($available_vars);
$variations_attr  = function_exists('wc_esc_json') ? wc_esc_json($variations_json) : _wp_specialchars($variations_json, ENT_QUOTES, 'UTF-8', true);



do_action('woocommerce_before_add_to_cart_form');
?>

<form class="variations_form cart" action="<?php echo esc_url(apply_filters('woocommerce_add_to_cart_form_action', $product->get_permalink())); ?>" method="post" enctype='multipart/form-data' data-product_id="<?php echo absint($product->get_id()); ?>" data-product_variations="<?php echo $variations_attr; ?>">
  <?php do_action('woocommerce_before_variations_form'); ?>

  <?php if (empty($available_vars) && false !== $available_vars) : ?>
    <p class="stock out-of-stock"><?php esc_html_e('This product is currently out of stock and unavailable.', 'woocommerce'); ?></p>
  <?php else : ?>
    <table class="variations" cellspacing="0">
      <tbody>
        <?php foreach ($attributes as $attribute_name => $options) : ?>
          <tr>
            <td class="label"><label for="<?php echo esc_attr(sanitize_title($attribute_name)); ?>"><?php echo wc_attribute_label($attribute_name); ?></label></td>
          </tr>
          <tr>
            <td class="value">
              <div class="variation-buttons">
                <?php
                $selected_value = isset($_REQUEST['attribute_' . sanitize_title($attribute_name)]) ? wc_clean(wp_unslash($_REQUEST['attribute_' . sanitize_title($attribute_name)])) : $product->get_variation_default_attribute($attribute_name);

                foreach ($options as $option) {
                  $matched_variation = null;

                  foreach ($available_vars as $variation) {
                    if ($variation['attributes']['attribute_' . sanitize_title($attribute_name)] === $option) {
                      $matched_variation = $variation;
                      break;
                    }
                  }

                  $price_html = $matched_variation ? $matched_variation['price_html'] : '';
                  $checked    = sanitize_title($selected_value) === sanitize_title($option) ? 'selected' : '';



                  echo '<div class="variation-button ' . esc_attr($checked) . '" data-attribute_name="attribute_' . esc_attr(sanitize_title($attribute_name)) . '" data-attribute_value="' . esc_attr($option) . '">';
                  //@todo change this rendering somehow, its now rendering the slug
                  // echo esc_html(wc_attribute_label($option));
                  $term_name = $option;
                  // Om attributet är en taxonomy (t.ex. pa_color)
                  if (taxonomy_exists($attribute_name)) {
                    $term = get_term_by('slug', $option, $attribute_name);
                    if ($term && ! is_wp_error($term)) {
                      $term_name = $term->name;
                    }
                  }

                  echo '<p>' . $term_name . '</p>';
                  // echo esc_html(get_term_by('slug', wc_attribute_taxonomy_name($option))->name);
                  if ($price_html) {
                    echo '<span class="variation-price">' . $price_html . '</span>';
                  }
                  echo '</div>';
                }
                ?>
              </div>

              <select name="attribute_<?php echo esc_attr(sanitize_title($attribute_name)); ?>" style="display:none;">
                <option value=""><?php esc_html_e('Choose an option', 'woocommerce'); ?>&hellip;</option>
                <?php
                foreach ($options as $option) {
                  echo '<option value="' . esc_attr($option) . '" ' . selected(sanitize_title($selected_value), sanitize_title($option), false) . '>' . esc_html(wc_attribute_label($option)) . '</option>';
                }
                ?>
              </select>
            </td>
          </tr>
        <?php endforeach; ?>
      </tbody>
    </table>

    <div class="selected-variation-price" style="margin: 10px 0; font-weight: bold;"></div>


    <?php do_action('woocommerce_before_add_to_cart_button'); ?>

    <div class="single_variation_wrap">
      <?php
      /**
       * Hook: woocommerce_before_single_variation.
       */
      do_action('woocommerce_before_single_variation');

      /**
       * Hook: woocommerce_single_variation.
       *
       * @hooked woocommerce_single_variation - 10
       * @hooked woocommerce_single_variation_add_to_cart_button - 20
       */
      do_action('woocommerce_single_variation');

      /**
       * Hook: woocommerce_after_single_variation.
       */
      do_action('woocommerce_after_single_variation');
      ?>
    </div>

    <?php do_action('woocommerce_after_add_to_cart_button'); ?>
  <?php endif; ?>

  <?php do_action('woocommerce_after_variations_form'); ?>
</form>

<?php do_action('woocommerce_after_add_to_cart_form'); ?>