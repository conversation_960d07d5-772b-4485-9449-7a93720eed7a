<?php
/**
 * Single Product Meta
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/meta.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see         https://woocommerce.com/document/template-structure/
 * @package     WooCommerce\Templates
 * @version     3.0.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

global $product;
?>
<div class="product_meta">
    <?php do_action( 'woocommerce_product_meta_start' ); ?>

    <?php if ( ! empty( $product->get_attributes() ) ) : ?>
        <div class="accordion">
            <div class="accordion-header">
                <span>Produktinformation</span>
                <button class="accordion-toggle">+</button>
            </div>
            <div class="accordion-content">
                <table class="product_attributes">
                    <tbody>
                        <?php foreach ( $product->get_attributes() as $attribute_slug => $attribute ) : ?>
                            <tr>
                                <th><?php echo esc_html( wc_attribute_label( $attribute->get_name() ) ); ?></th>
                                <td>
                                    <?php 
                                        if ( $attribute->is_taxonomy() ) {
                                            $terms = wp_get_post_terms( $product->get_id(), $attribute->get_name(), array( 'fields' => 'names' ) );
                                            echo esc_html( implode( ', ', $terms ) );
                                        } else {
                                            echo esc_html( implode( ', ', $attribute->get_options() ) );
                                        }
                                    ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
</div>


<script>

document.addEventListener("DOMContentLoaded", function () {
    var accordionHeader = document.querySelector(".accordion-header");
    var accordionContent = document.querySelector(".accordion-content");
    var accordionToggle = document.querySelector(".accordion-toggle");

    // Standard: Öppen på desktop, stängd på mobil
    if (window.innerWidth <= 768) {
        accordionContent.style.display = "none";
        accordionToggle.textContent = "+";
    } else {
        accordionContent.style.display = "block";
        accordionToggle.textContent = "−";
    }

    accordionHeader.addEventListener("click", function () {
        if (accordionContent.style.display === "block") {
            accordionContent.style.display = "none";
            accordionToggle.textContent = "+";
        } else {
            accordionContent.style.display = "block";
            accordionToggle.textContent = "−";
        }
    });
});

</script>