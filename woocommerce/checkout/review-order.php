<?php
defined('ABSPATH') || exit;
?>

<!-- Steg-indikator -->
<div class="checkout-steps">
    <div class="step completed">1. <PERSON><PERSON><PERSON><PERSON> produkter</div>
    <div class="step active">2. Checka ut</div>
    <div class="step">3. Vi levererar</div>
</div>

<div class="checkout-review-order">
    <?php foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) :
        $_product = $cart_item['data'];
        if ($_product && $_product->exists() && $cart_item['quantity'] > 0) : ?>

            <div class="cart-item">
                <!-- Bildkolumn -->
                <div class="cart-item-image">
                    <?php echo $_product->get_image(); ?>
                </div>

                <!-- Detaljer och antal -->
                <div class="cart-item-details">
                    <h4 class="cart-item-title"> <?php echo $_product->get_name(); ?> </h4>
                    <div class="cart-item-quantity">
                        <button class="quantity-btn minus" data-cart-key="<?php echo esc_attr($cart_item_key); ?>">-</button>
                        <input type="number" class="cart-quantity" value="<?php echo esc_attr($cart_item['quantity']); ?>" min="1" readonly>
                        <button class="quantity-btn plus" data-cart-key="<?php echo esc_attr($cart_item_key); ?>">+</button>
                    </div>
                    <div class="cart-item-price"> <?php echo wc_price($_product->get_price()); ?> / st</div>
                </div>

                <!-- Totalpris per rad -->
                <div class="cart-item-total">
                    <?php echo WC()->cart->get_product_subtotal($_product, $cart_item['quantity']); ?>
                </div>
            </div>
    <?php endif;
    endforeach; ?>
</div>

<!-- Shipping Options -->
<?php if (WC()->cart->needs_shipping() && WC()->cart->show_shipping()) : ?>
    <div class="shipping-options">
        <h3><?php esc_html_e('Shipping', 'woocommerce'); ?></h3>
        <?php do_action('woocommerce_review_order_before_shipping'); ?>
        <?php wc_cart_totals_shipping_html(); ?>
        <?php do_action('woocommerce_review_order_after_shipping'); ?>
    </div>
<?php endif; ?>

<!-- Order Totals -->
<div class="checkout-totals">
    <div class="totals-line">
        <span><?php esc_html_e('Subtotal', 'woocommerce'); ?>:</span>
        <span><?php wc_cart_totals_subtotal_html(); ?></span>
    </div>

    <?php if (WC()->cart->needs_shipping() && WC()->cart->show_shipping()) : ?>
        <div class="totals-line">
            <span><?php esc_html_e('Shipping', 'woocommerce'); ?>:</span>
            <span class="shipping-cost-display">
                <?php
                // Hämta vald fraktmetod och dess kostnad
                $chosen_methods = WC()->session->get('chosen_shipping_methods');
                $shipping_packages = WC()->shipping->get_packages();
                $shipping_cost_found = false;

                if (! empty($shipping_packages) && ! empty($chosen_methods)) {
                    foreach ($shipping_packages as $i => $package) {
                        if (isset($chosen_methods[$i]) && isset($package['rates'][$chosen_methods[$i]])) {
                            $rate = $package['rates'][$chosen_methods[$i]];
                            $total_cost = $rate->cost;

                            // Lägg till eventuell fraktmoms
                            if (isset($rate->taxes) && is_array($rate->taxes)) {
                                $total_cost += array_sum($rate->taxes);
                            }

                            if ($total_cost == 0) {
                                echo __('Free', 'woocommerce');
                            } else {
                                echo wc_price($total_cost);
                            }
                            $shipping_cost_found = true;
                            break;
                        }
                    }
                }

                // Fallback om ingen fraktmetod hittades
                if (! $shipping_cost_found) {
                    $shipping_total = WC()->cart->get_shipping_total();
                    $shipping_tax = WC()->cart->get_shipping_tax();
                    $total_shipping = $shipping_total + $shipping_tax;

                    if ($total_shipping > 0) {
                        echo wc_price($total_shipping);
                    } else {
                        echo wc_price(0);
                    }
                }
                ?>
            </span>
        </div>
    <?php endif; ?>

    <?php foreach (WC()->cart->get_coupons() as $code => $coupon) : ?>
        <div class="totals-line">
            <span><?php wc_cart_totals_coupon_label($coupon); ?>:</span>
            <span><?php wc_cart_totals_coupon_html($coupon); ?></span>
        </div>
    <?php endforeach; ?>

    <?php if (wc_tax_enabled() && ! WC()->cart->display_prices_including_tax()) : ?>
        <?php if ('itemized' === get_option('woocommerce_tax_total_display')) : ?>
            <?php foreach (WC()->cart->get_tax_totals() as $code => $tax) : ?>
                <div class="totals-line">
                    <span><?php echo esc_html($tax->label); ?>:</span>
                    <span><?php echo wp_kses_post($tax->formatted_amount); ?></span>
                </div>
            <?php endforeach; ?>
        <?php else : ?>
            <div class="totals-line">
                <span><?php echo esc_html(WC()->countries->tax_or_vat()); ?>:</span>
                <span><?php wc_cart_totals_taxes_total_html(); ?></span>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <div class="totals-line total-line">
        <span><?php esc_html_e('Total', 'woocommerce'); ?>:</span>
        <span><?php wc_cart_totals_order_total_html(); ?></span>
    </div>
</div>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        <<
        << << < HEAD
            // Hantera kvantitetsändringar
            ===
            === = >>>
            >>> > c82acab(initial commit)
        document.querySelectorAll('.quantity-btn').forEach(button => {
            button.addEventListener('click', function() {
                let cartKey = this.getAttribute('data-cart-key');
                let input = this.closest('.cart-item-quantity').querySelector('.cart-quantity');
                let newValue = parseInt(input.value); <<
                << << < HEAD

                    ===
                    === =

                    >>>
                    >>> > c82acab(initial commit)
                if (this.classList.contains('plus')) {
                    newValue++;
                } else if (this.classList.contains('minus') && newValue > 1) {
                    newValue--;
                } <<
                << << < HEAD

                input.value = newValue;

                ===
                === =

                input.value = newValue;

                >>>
                >>> > c82acab(initial commit)
                fetch('<?php echo esc_url(admin_url('admin-ajax.php')); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: `action=update_cart_quantity&cart_item_key=${cartKey}&quantity=${newValue}`
                }).then(() => location.reload());
            });
        }); <<
        << << < HEAD

        // Hantera fraktalternativ ändringar
        document.querySelectorAll('input[name^="shipping_method"]').forEach(radio => {
            radio.addEventListener('change', function() {
                // Uppdatera visuell feedback omedelbart
                document.querySelectorAll('.shipping-options li').forEach(li => {
                    li.classList.remove('selected');
                });
                this.closest('li').classList.add('selected');

                // AJAX-uppdatering av fraktkostnad
                const formData = new FormData();
                formData.append('action', 'update_shipping_method');
                formData.append('shipping_method', this.value);
                formData.append('nonce', '<?php echo wp_create_nonce("update_shipping_nonce"); ?>');

                fetch('<?php echo admin_url("admin-ajax.php"); ?>', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Uppdatera fraktkostnad med specifik klass
                            const shippingCostDisplay = document.querySelector('.shipping-cost-display');
                            if (shippingCostDisplay && data.data.shipping_cost) {
                                shippingCostDisplay.innerHTML = data.data.shipping_cost;
                            }

                            // Alternativ metod - hitta via text
                            if (!shippingCostDisplay) {
                                const shippingLines = document.querySelectorAll('.checkout-totals .totals-line');
                                shippingLines.forEach(line => {
                                    const label = line.querySelector('span:first-child');
                                    if (label && (label.textContent.includes('Shipping') || label.textContent.includes('Frakt'))) {
                                        const costSpan = line.querySelector('span:last-child');
                                        if (costSpan && data.data.shipping_cost) {
                                            costSpan.innerHTML = data.data.shipping_cost;
                                        }
                                    }
                                });
                            }

                            // Uppdatera totalsumma
                            const totalLine = document.querySelector('.checkout-totals .total-line span:last-child');
                            if (totalLine && data.data.total) {
                                totalLine.innerHTML = data.data.total;
                            }

                            console.log('Shipping updated successfully:', data.data);
                        } else {
                            console.error('Failed to update shipping:', data);
                        }
                    })
                    .catch(error => {
                        console.error('Error updating shipping:', error);
                    });
            });
        });

        // Sätt initial vald fraktmetod
        const checkedShipping = document.querySelector('input[name^="shipping_method"]:checked');
        if (checkedShipping) {
            checkedShipping.closest('li').classList.add('selected');
        } ===
        === = >>>
        >>> > c82acab(initial commit)
    });
</script>

<style>
    .checkout-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    <<<<<<< HEAD .cart-item-price {
        margin-top: 15px;
    }

    .totals-line {
        padding: 10px !important;
    }

    =======>>>>>>>c82acab (initial commit) .checkout-steps .step {
        flex: 1;
        text-align: center;
        padding: 10px;
        background: #f1f1f1;
        border-radius: 5px;
        margin-right: 5px;
        text-transform: uppercase;
        <<<<<<< HEAD font-family: 'CustomHeadingFont';
    }

    .checkout-steps .active {
        background: var(--accentColor);
        color: #000;
        =======font-family: 'CustomHeadingFont';
    }

    .checkout-steps .active {
        background: var(--accentColor);
        color: white;
        >>>>>>>c82acab (initial commit)
    }

    .cart-item {
        display: flex;
        align-items: center;
        background: #f9f9f9;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 5px;
    }

    .cart-item-image img {
        width: 80px;
        height: auto;
    }

    .cart-item-details {
        flex: 2;
        padding-left: 15px;
    }

    .cart-item-title {
        margin: 0 0 5px;
        font-size: 16px;
    }

    .cart-item-quantity {
        display: flex;
        align-items: center;
    }

    .cart-quantity {
        text-align: center;
        width: 40px;
    }

    .quantity-btn {
        background: #ddd;
        border: none;
        padding: 5px 10px;
        cursor: pointer;
    }

    .quantity-btn:hover {
        background: #ccc;
    }

    .cart-item-total {
        flex: 1;
        text-align: right;
        font-weight: bold;
    }

    .checkout-totals {
        background: #fff;
        padding: 15px;
        border-top: 1px solid #ddd;
    }

    <<<<<<< HEAD

    /* Shipping Options Styling */
    .shipping-options {
        background: #f9f9f9;
        padding: 20px;
        margin: 20px 0;
        border-radius: 5px;
        border: 1px solid #e0e0e0;
    }

    .shipping-options h3 {
        margin: 0 0 15px 0;
        text-transform: uppercase;
        font-size: 1.2rem;
        color: #000;
    }

    .shipping-options ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .shipping-options li {
        margin-bottom: 10px;
        padding: 15px;
        background: #fff;
        border-radius: 8px;
        border: 2px solid #ddd;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .shipping-options li:hover {
        border-color: #d5fa8f;
        background: #f8fff0;
    }

    .shipping-options li input[type="radio"]:checked+label {
        font-weight: bold;
    }

    .shipping-options li:has(input[type="radio"]:checked) {
        border-color: #d5fa8f;
        background: #f0fff0;
        box-shadow: 0 2px 8px rgba(213, 250, 143, 0.3);
    }

    .shipping-options input[type="radio"] {
        margin-right: 12px;
        width: 18px;
        height: 18px;
        accent-color: #d5fa8f;
    }

    .shipping-options label {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        font-weight: 500;
        width: 100%;
        margin: 0;
    }

    .shipping-options .woocommerce-Price-amount {
        font-weight: bold;
        color: #000;
        font-size: 1.1rem;
    }

    /* Förbättra radio button styling */
    .shipping-options input[type="radio"] {
        appearance: none;
        width: 20px;
        height: 20px;
        border: 2px solid #ddd;
        border-radius: 50%;
        margin-right: 12px;
        position: relative;
        cursor: pointer;
    }

    .shipping-options input[type="radio"]:checked {
        border-color: #d5fa8f;
        background: #d5fa8f;
    }

    .shipping-options input[type="radio"]:checked::after {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #000;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    /* Extra styling för vald fraktmetod */
    .shipping-options li.selected {
        border-color: #d5fa8f !important;
        background: #f0fff0 !important;
        box-shadow: 0 2px 8px rgba(213, 250, 143, 0.3) !important;
    }

    .shipping-options li.selected label {
        font-weight: bold !important;
    }

    /* Responsiv design för fraktalternativ */
    @media (max-width: 768px) {
        .shipping-options {
            padding: 15px;
            margin: 15px 0;
        }

        .shipping-options li {
            padding: 12px;
        }

        .shipping-options label {
            font-size: 14px;
        }
    }

    .totals-line {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }

    .totals-line.total-line {
        border-bottom: none;
        border-top: 2px solid #000;
        padding-top: 15px;
        margin-top: 10px;
        font-weight: bold;
        font-size: 1.1rem;
    }

    =======>>>>>>>c82acab (initial commit) .cart-quantity {
        -moz-appearance: textfield;
        -webkit-appearance: textfield;
        appearance: textfield;
    }

    .cart-quantity::-webkit-inner-spin-button,
    .cart-quantity::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    <<<<<<< HEAD @media (max-width: 768px) {
        .checkout-steps {
            flex-direction: column;
        }
    }

    a.showcoupon {
        color: var(--accentColor);
    }

    .quantity-btn {
        background: #000;
        border: none;
        padding: 5px 10px;
        cursor: pointer;
        border-radius: 50%;
        color: #000;
    }

    .cart-item-title {
        margin: 0 0 5px;
        font-size: 16px;
        text-transform: uppercase;
    }

    .cart-quantity {
        text-align: center;
        width: 40px;
        background-color: transparent;
        background: transparent;
        border: 0px;
        font-weight: 700;
    }

    .woocommerce-checkout .page-content {
        max-width: 90vw;
        margin: 20px auto;
    }

    =======>>>>>>>c82acab (initial commit)
</style>