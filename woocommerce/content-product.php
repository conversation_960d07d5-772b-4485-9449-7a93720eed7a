<?php
defined( 'ABSPATH' ) || exit;

global $product;

// Säkerställ synlighet.
if ( empty( $product ) || ! $product->is_visible() ) {
    return;
}

// Hämta lagerstatus
$stock_status = $product->get_stock_status();
$backorders = $product->backorders_allowed();
$stock_text = '';
$stock_class = '';

if ( $stock_status === 'instock' && !$backorders ) {
    $stock_text = 'Leverans 1-2 dagar';
    $stock_class = 'stock-green';
} elseif ( $stock_status === 'onbackorder' ) {
    $stock_text = 'Leverans 6-10 dagar';
    $stock_class = 'stock-yellow';
} else {
    $stock_text = 'Slut i lager';
    $stock_class = 'stock-red';
}
?>

<li <?php wc_product_class( 'custom-product-card', $product ); ?>>
    <div class="product-card">
        <?php
        /**
         * Hook: woocommerce_before_shop_loop_item.
         *
         * @hooked woocommerce_template_loop_product_link_open - 10
         */
        do_action( 'woocommerce_before_shop_loop_item' );

        /**
         * Hook: woocommerce_before_shop_loop_item_title.
         *
         * @hooked woocommerce_template_loop_product_thumbnail - 10
         */
        do_action( 'woocommerce_before_shop_loop_item_title' );
        ?>

        <div class="product-info">

			<div class="title-stock-div">
            <?php
            /**
             * Hook: woocommerce_shop_loop_item_title.
             *
             * @hooked woocommerce_template_loop_product_title - 10
             */
            do_action( 'woocommerce_shop_loop_item_title' );

            ?>

					<!-- Lagerstatus -->
			<div class="stock-status <?php echo esc_attr($stock_class); ?>">
            <span class="stock-dot"></span> <?php echo esc_html($stock_text); ?>
            </div>
		</div>

            <?php
			          /**
             * Hook: woocommerce_after_shop_loop_item_title.
             *
             * @hooked woocommerce_template_loop_price - 10
             */
            do_action( 'woocommerce_after_shop_loop_item_title' );
            
			
            /**
             * Hook: woocommerce_after_shop_loop_item.
             *
             * @hooked woocommerce_template_loop_product_link_close - 5
             * @hooked woocommerce_template_loop_add_to_cart - 10
             */
            do_action( 'woocommerce_after_shop_loop_item' );
            ?>

			<!-- Köp produkt-knapp -->
            <a href="<?php echo esc_url( get_permalink( $product->get_id() ) ); ?>" class="buy-product-button">
                Köp produkt +
            </a>
        </div>
    </div>
</li>
