<?php

/**
 * Custom content-single-product.php
 */
defined('ABSPATH') || exit;

global $product;

do_action('woocommerce_before_single_product');

if (post_password_required()) {
    echo get_the_password_form();
    return;
}
?>

<div id="product-<?php the_ID(); ?>" <?php wc_product_class(); ?>>
    <div class="product-columns-wrapper fade-in">
        <!-- Left Column: Custom Image Gallery -->
        <div class="product-image-gallery">
            <?php
            $attachment_ids = $product->get_gallery_image_ids();
            $featured_image = get_post_thumbnail_id() ? wp_get_attachment_url(get_post_thumbnail_id()) : '';

            if ($featured_image || !empty($attachment_ids)): ?>
                <div class="product-slider">
                    <div class="main-image-wrapper">
                        <button class="slider-arrow prev">&#10094;</button>
                        <div class="main-image-container">
                            <!-- Huvudbild med Fancybox -->
                            <a id="main-image-link" data-fancybox="product-gallery" href="<?php echo esc_url($featured_image); ?>">
                                <img id="main-image" src="<?php echo esc_url($featured_image); ?>" alt="Produktbild">
                            </a>
                        </div>
                        <button class="slider-arrow next">&#10095;</button>
                    </div>

                    <!-- Thumbnails (byter bild men öppnar ej fancybox) -->
                    <div class="thumbnail-wrapper">
                        <?php if ($featured_image): ?>
                            <img class="thumbnail active" src="<?php echo esc_url($featured_image); ?>" data-full="<?php echo esc_url($featured_image); ?>">
                        <?php endif; ?>

                        <?php foreach ($attachment_ids as $attachment_id):
                            $thumb_url = wp_get_attachment_image_url($attachment_id, 'thumbnail');
                            $full_url = wp_get_attachment_url($attachment_id);
                        ?>
                            <img class="thumbnail" src="<?php echo esc_url($thumb_url); ?>" data-full="<?php echo esc_url($full_url); ?>">
                        <?php endforeach; ?>
                    </div>

                    <!-- Dolda fancybox-länkar (syns inte, men gör att fancybox kan bläddra mellan bilderna) -->
                    <div style="display: none;">
                        <?php foreach ($attachment_ids as $attachment_id):
                            $full_url = wp_get_attachment_url($attachment_id);
                        ?>
                            <a href="<?php echo esc_url($full_url); ?>" data-fancybox="product-gallery"></a>
                        <?php endforeach; ?>
                    </div>
                </div>


            <?php endif; ?>



        </div>

        <!-- Right Column: Product Summary -->
        <div class="product-summary">




            <?php do_action('woocommerce_single_product_summary'); ?>
        </div>
    </div>
</div>

<!-- TAAAABS  -->
<div class="custom-tabs-wrapper fade-in" id="tabs-content">
    <h2 class="tabs-main-title">Fullständig produktinformation</h2>

    <?php
    // Kontrollera om innehåll finns för varje sektion
    $produktbeskrivning_innehall = !empty($product->get_description());
    $teknisk_specifikation_innehall = get_field('teknisk_specifikation');
    $filer = get_field('filer');
    $video_innehall = get_field('video_url');
    $installationsguide_innehall = get_field('video_url_installationsguide', 'options');
    ?>

    <!-- Tabbar -->
    <div class="custom-tabs">
        <?php if ($produktbeskrivning_innehall): ?>
            <button class="tab-button active" data-tab="produktbeskrivning">Produktbeskrivning</button>
        <?php endif; ?>
        <?php if ($teknisk_specifikation_innehall || $filer): ?>
            <button class="tab-button" data-tab="teknisk-specifikation">Teknisk specifikation</button>
        <?php endif; ?>
        <?php if ($video_innehall): ?>
            <button class="tab-button" data-tab="video">Video</button>
        <?php endif; ?>
        <?php if ($installationsguide_innehall): ?>
            <button class="tab-button" data-tab="installationsguide">Installationsguide</button>
        <?php endif; ?>
    </div>

    <!-- Tab Innehåll -->
    <?php if ($produktbeskrivning_innehall): ?>
        <div class="tab-content active" id="produktbeskrivning">
            <div class="wysiwyg-content-container">
                <div class="wysiwyg-content">
                    <?php echo wp_kses_post($product->get_description()); ?>
                </div>
                <div class="fade-overlay"></div> <!-- Fade-effekt -->
            </div>
            <button class="las-mer-btn" onclick="toggleDescription()">Läs mer</button>
        </div>
    <?php endif; ?>


    <?php if ($teknisk_specifikation_innehall || $filer): ?>
        <div class="tab-content" id="teknisk-specifikation">
            <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>" target="_blank">
                <input type="hidden" name="action" value="generate_product_pdf_tcpdf">
                <input type="hidden" name="product_id" value="<?php echo get_the_ID(); ?>">
                <button type="submit" class="generate-pdf-button">Generera produkt PDF</button>
            </form>
            <div class="wysiwyg-content">
                <?php echo wp_kses_post($teknisk_specifikation_innehall); ?>
                <?php if ($filer): ?>
                    <h2 class="product-documents-title">Produktdokument</h2>
                    <div class="product-documents">
                        <?php foreach ($filer as $file): ?>
                            <?php
                            $pdf_file = $file['pdf'];
                            $file_name = $file['file_name'] ? $file['file_name'] : 'Ladda ner PDF';
                            ?>
                            <div class="document-item">
                                <a href="<?php echo esc_url($pdf_file['url']); ?>" target="_blank" class="pdf-download-btn">
                                    <i class="fas fa-file-pdf"></i>
                                    <?php echo esc_html($file_name); ?>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <?php
    $video_id = get_field('video_url'); // Använd bara video-ID, t.ex. "UFkgEaPFP5Q"

    if ($video_id):
        $thumbnail_url = "https://img.youtube.com/vi/$video_id/maxresdefault.jpg";
    ?>

        <div class="tab-content" id="video">
            <!-- Video Thumbnail med knapp -->
            <div class="video-thumbnail-container-sp" onclick="openVideoPopup('<?php echo esc_js($video_id); ?>')">
                <img src="<?php echo esc_url($thumbnail_url); ?>" alt="Video Thumbnail" class="video-thumbnail">
                <button class="video-play-button">▶ Se video</button>
            </div>
        </div>

        <!-- Popup-overlay för videon -->
        <div id="video-popup" class="video-popup">
            <div class="video-popup-content">
                <span class="close-popup" onclick="closeVideoPopup()">&times;</span>
                <iframe id="video-iframe" width="100%" height="500" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    <?php endif; ?>

    <?php
    $video_id_installationsguide = get_field('video_url_installationsguide', 'option'); // Använd bara video-ID, t.ex. "UFkgEaPFP5Q"

    if ($video_id_installationsguide):
        $thumbnail_url_installationsguide = "https://img.youtube.com/vi/$video_id_installationsguide/maxresdefault.jpg";
    ?>
        <div class="tab-content" id="installationsguide">
            <!-- Video Thumbnail med knapp -->
            <div class="video-thumbnail-container-sp" onclick="openVideoPopup('<?php echo esc_js($video_id_installationsguide); ?>')">
                <img src="<?php echo esc_url($thumbnail_url_installationsguide); ?>" alt="Video Thumbnail" class="video-thumbnail">
                <button class="video-play-button">▶ Se video</button>
            </div>
        </div>

        <!-- Popup-overlay för videon -->
        <div id="video-popup" class="video-popup">
            <div class="video-popup-content">
                <span class="close-popup" onclick="closeVideoPopup()">&times;</span>
                <iframe id="video-iframe" width="100%" height="500" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    <?php endif; ?>


</div>



<!---- HOTSPOTS ------>

<?php
// Hämta ACF-fält för båda bilderna
$hotspot_bild_1 = get_field('hotspot_bild_1');
$hotspot_punkter_1 = get_field('hotspot_punkter_1');

$hotspot_bild_2 = get_field('hotspot_bild_2');
$hotspot_punkter_2 = get_field('hotspot_punkter_2');

// Kontrollera att minst en bild finns
if ($hotspot_bild_1 || $hotspot_bild_2): ?>
    <div class="hotspot-section-container fade-in">
        <?php if ($hotspot_bild_1): ?>
            <div class="hotspot-container half-width">
                <img src="<?php echo esc_url($hotspot_bild_1); ?>" alt="Hotspot Bild 1" style="width: 100%; height: auto;">
                <?php if ($hotspot_punkter_1): ?>
                    <?php foreach ($hotspot_punkter_1 as $punkt): ?>
                        <div class="hotspot" style="left: <?php echo esc_attr($punkt['hotspot_x']); ?>%; top: <?php echo esc_attr($punkt['hotspot_y']); ?>%;">
                            <div class="hotspot-dot"></div>
                            <div class="hotspot-info"><?php echo esc_html($punkt['hotspot_info']); ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?php if ($hotspot_bild_2): ?>
            <div class="hotspot-container half-width">
                <img src="<?php echo esc_url($hotspot_bild_2); ?>" alt="Hotspot Bild 2" style="width: 100%; height: auto;">
                <?php if ($hotspot_punkter_2): ?>
                    <?php foreach ($hotspot_punkter_2 as $punkt): ?>
                        <div class="hotspot" style="left: <?php echo esc_attr($punkt['hotspot_x']); ?>%; top: <?php echo esc_attr($punkt['hotspot_y']); ?>%;">
                            <div class="hotspot-dot"></div>
                            <div class="hotspot-info"><?php echo esc_html($punkt['hotspot_info']); ?></div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
<?php endif; ?>



<!---- Produktbild slider ----->

<div class="reviews-section-ks">
    <div class="inner-section-reviews">
        <h2 class="reviews-title" id="reviews">Produktrecensioner</h2>
        <?php echo do_shortcode('[cusrev_all_reviews sort="DESC" sort_by="date" per_page="10" number="-1" show_summary_bar="true" show_products="true" categories="" product_tags="" tags="" products="current" shop_reviews="true" number_shop_reviews="-1" inactive_products="false" show_replies="false" show_more="3" min_chars="0" avatars="initials" users="all" add_review="true"]') ?>
    </div>
</div>


<!--- Senaste besökta produkter ---->
<?php if (function_exists('wc_get_product')): ?>
    <div class="custom-tabs-wrapper recent-products-section fade-in" id="recently-viewed-container" style="display: none;">
        <h2 class="tabs-main-title">Senaste besökta produkter</h2>
        <div class="swiper-container swiper-container-recent product-carousel woocommerce">
            <div class="swiper-wrapper" id="recently-viewed-products">
                <!-- Produkter laddas dynamiskt via JavaScript -->
            </div>
            <!-- Navigeringsknappar -->
            <div class="pagination-wrapper">
                <div class="swiper-button-prev recent-prev"></div>
                <div class="swiper-button-next recent-next"></div>
            </div>
        </div>
    </div>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {

        const currentProductId = <?php echo get_the_ID(); ?>;

        let viewedProducts = [];
        if (localStorage.getItem('smort_recently_viewed')) {
            try {
                viewedProducts = JSON.parse(localStorage.getItem('smort_recently_viewed'));

                if (!Array.isArray(viewedProducts)) {
                    viewedProducts = [];
                }
            } catch (e) {
                viewedProducts = [];
            }
        }


        viewedProducts = viewedProducts.filter(id => id !== currentProductId);


        viewedProducts.unshift(currentProductId);

        viewedProducts = viewedProducts.slice(0, 10);

        localStorage.setItem('smort_recently_viewed', JSON.stringify(viewedProducts));

        const displayProducts = viewedProducts.filter(id => id !== currentProductId);

        if (displayProducts.length > 0) {
            fetchRecentlyViewedProducts(displayProducts);
        }


        function fetchRecentlyViewedProducts(productIds) {

            const data = new FormData();
            data.append('action', 'get_recently_viewed_products');
            data.append('product_ids', JSON.stringify(productIds));


            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                    method: 'POST',
                    body: data
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {

                        document.getElementById('recently-viewed-products').innerHTML = data.data;

                        document.getElementById('recently-viewed-container').style.display = 'block';

                        new Swiper('.swiper-container-recent', {
                            loop: true,
                            navigation: {
                                nextEl: '.recent-next',
                                prevEl: '.recent-prev',
                            },
                            slidesPerView: 1,
                            spaceBetween: 10,
                            breakpoints: {
                                0: {
                                    slidesPerView: 1,
                                },
                                1000: {
                                    slidesPerView: 2.5,
                                },
                                1500: {
                                    slidesPerView: 3.5,
                                },
                                2000: {
                                    slidesPerView: 4.5,
                                },
                            },
                        });
                    }
                })
                .catch(error => {
                    console.error('Error fetching recently viewed products:', error);
                });
        }
    });
</script>

<!---- Relaterade produkter ----->
<div class="related-div-outer">
    <div class="custom-tabs-wrapper related-products-section fade-in">
        <h2 class="tabs-main-title">Relaterade produkter</h2>

        <?php
        global $product;

        // Hämta produktens kategori-ID
        $terms = get_the_terms($product->get_id(), 'product_cat');

        if ($terms && !is_wp_error($terms)) {
            $category_ids = wp_list_pluck($terms, 'term_id');

            // Hämta relaterade produkter från samma kategori
            $args = array(
                'post_type'      => 'product',
                'posts_per_page' => 10, // Antal produkter
            );

            $related_query = new WP_Query($args);

            if ($related_query->have_posts()) : ?>
                <div class="swiper-container swiper-container-2 product-carousel woocommerce">
                    <div class="swiper-wrapper">


                        <?php while ($related_query->have_posts()) : $related_query->the_post(); ?>
                            <div class="swiper-slide product-slide">
                                <?php wc_get_template_part('content', 'product'); ?>
                            </div>
                        <?php endwhile; ?>

                        <!-- Navigeringsknappar -->
                    </div>
                    <div class="pagination-wrapper">
                        <div class="swiper-button-prev related-prev"></div>
                        <div class="swiper-button-next related-next"></div>
                    </div>
                </div>
            <?php else: ?>
                <p>Inga relaterade produkter hittades.</p>
        <?php endif;
            wp_reset_postdata();
        } ?>
    </div>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        new Swiper('.swiper-container-2', {
            slidesPerView: 4,
            loop: true,
            navigation: {
                nextEl: '.related-next',
                prevEl: '.related-prev',
            },
            slidesPerView: 1,
            spaceBetween: 10,
            breakpoints: {
                0: {
                    // For screens smaller than 480px
                    slidesPerView: 1, // Show 1 slide
                },
                1000: {
                    // For screens between 481px and 768px
                    slidesPerView: 2.5, // Show 2.5 slides
                },
                1500: {
                    // For screens larger than 768px
                    slidesPerView: 3.5, // Show 3.5 slides
                },
                2000: {
                    // For screens larger than 768px
                    slidesPerView: 4.5, // Show 3.5 slides
                },
            },
        });
    });
</script>



<script>
    document.addEventListener('DOMContentLoaded', () => {
        const addons = document.querySelectorAll('.yith-wapo-addon');

        addons.forEach(addon => {
            const options = addon.querySelectorAll('.yith-wapo-option');
            if (options.length === 1) {
                addon.style.display = 'none';
            }
        });
    });


    document.addEventListener('DOMContentLoaded', function() {
        // Loopar igenom alla tabbsektioner
        document.querySelectorAll('.custom-tabs-wrapper').forEach((wrapper) => {
            const tabs = wrapper.querySelectorAll('.tab-button');
            const contents = wrapper.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Ta bort "active" endast inom den aktuella tabbgruppen
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));

                    // Lägg till "active" på den valda tabben och innehållet
                    tab.classList.add('active');
                    const target = wrapper.querySelector(`#${tab.getAttribute('data-tab')}`);
                    if (target) {
                        target.classList.add('active');
                    }
                });
            });
        });
    });


    /* Video player */

    function openVideoPopup(videoId) {
        const videoIframe = document.getElementById("video-iframe");
        const videoPopup = document.getElementById("video-popup");

        videoIframe.src = "https://www.youtube.com/embed/" + videoId + "?autoplay=1";
        videoPopup.classList.add("active");
    }

    function closeVideoPopup() {
        const videoPopup = document.getElementById("video-popup");
        const videoIframe = document.getElementById("video-iframe");

        videoPopup.classList.remove("active");
        videoIframe.src = ""; // Stoppar videon när popupen stängs
    }

    document.addEventListener("DOMContentLoaded", function() {
        const contentContainer = document.querySelector(".wysiwyg-content-container");
        const content = document.querySelector(".wysiwyg-content");
        const readMoreButton = document.querySelector(".las-mer-btn");

        // Kontrollera om texten är längre än 250px
        if (content.scrollHeight > 450) {
            contentContainer.classList.add("overflow"); // Lägg till klass för att visa knappen
            readMoreButton.style.display = "block"; // Visa knappen
        }

        // Funktion för att expandera eller kollapsa texten
        window.toggleDescription = function() {
            contentContainer.classList.toggle("expanded");
            if (contentContainer.classList.contains("expanded")) {
                contentContainer.style.maxHeight = content.scrollHeight + "px";
                readMoreButton.textContent = "Visa mindre";
            } else {
                contentContainer.style.maxHeight = "450px";
                readMoreButton.textContent = "Läs mer";
            }
        };
    });



    document.addEventListener('DOMContentLoaded', function() {
        const slides = document.querySelectorAll('.produkt-slide');
        const prevButton = document.querySelector('.slider-nav.prev');
        const nextButton = document.querySelector('.slider-nav.next');
        const progressIndicator = document.querySelector('.indicator-progress');

        let currentIndex = 0;

        function updateSlider() {
            const slider = document.querySelector('.produkt-slider');
            slider.style.transform = `translateX(-${currentIndex * 100}%)`;
            progressIndicator.style.width = `${((currentIndex + 1) / slides.length) * 100}%`;
        }

        prevButton.addEventListener('click', () => {
            currentIndex = (currentIndex === 0) ? slides.length - 1 : currentIndex - 1;
            updateSlider();
        });

        nextButton.addEventListener('click', () => {
            currentIndex = (currentIndex === slides.length - 1) ? 0 : currentIndex + 1;
            updateSlider();
        });

        // Initiera indikator
        updateSlider();
    });



    document.addEventListener('DOMContentLoaded', () => {
        const sections = document.querySelectorAll('.fade-in');

        // Funktion som kollar om en sektion är synlig
        const isVisible = (element) => {
            const rect = element.getBoundingClientRect();
            return (
                rect.top <= window.innerHeight && rect.bottom >= 0
            );
        };

        const handleScroll = () => {
            sections.forEach((section) => {
                if (isVisible(section)) {
                    section.classList.add('show');
                } else {
                    section.classList.remove('show'); // Ta bort show för att animera igen
                }
            });
        };

        // Kör funktionen på scroll och vid laddning
        window.addEventListener('scroll', handleScroll);
        handleScroll(); // Kör en gång vid sidladdning
    });


    // BILDGALLERI PRODUKTBILDER 
    document.addEventListener('DOMContentLoaded', function() {
        const thumbnails = document.querySelectorAll('.thumbnail');
        const mainImage = document.getElementById('main-image');
        const mainImageLink = document.getElementById('main-image-link');
        const prevBtn = document.querySelector('.slider-arrow.prev');
        const nextBtn = document.querySelector('.slider-arrow.next');
        const thumbnailWrapper = document.querySelector('.thumbnail-wrapper');
        let currentIndex = 0;
        let thumbnailIndex = 0;

        function updateMainImage(index) {
            const newSrc = thumbnails[index].getAttribute('data-full');
            mainImage.setAttribute('src', newSrc);
            mainImageLink.setAttribute('href', newSrc);
            thumbnails.forEach(t => t.classList.remove('active'));
            thumbnails[index].classList.add('active');
            currentIndex = index;
        }

        function scrollToThumbnail(index) {
            if (thumbnails.length === 1) return;

            const thumbnail = thumbnails[index];
            const width = thumbnail.offsetWidth;
            if (index === 0) {
                thumbnailWrapper.scrollLeft = 0;
            } else if (index > thumbnailIndex) {
                thumbnailWrapper.scrollLeft += (width + 10)
            } else {
                thumbnailWrapper.scrollLeft -= (width + 10);
            }
            thumbnailIndex = newIndex;
        }

        thumbnails.forEach((thumb, index) => {
            thumb.addEventListener('click', () => {
                updateMainImage(index);
                scrollToThumbnail(index);
            });
        });

        prevBtn.addEventListener('click', () => {
            let newIndex = (currentIndex - 1 + thumbnails.length) % thumbnails.length;
            updateMainImage(newIndex);
            scrollToThumbnail(newIndex);
        });

        nextBtn.addEventListener('click', () => {
            let newIndex = (currentIndex + 1) % thumbnails.length;
            updateMainImage(newIndex);
            scrollToThumbnail(newIndex);
        });

        // Initiera
        updateMainImage(currentIndex);
    });



    // Bildgalleri längre ned 


    document.addEventListener('DOMContentLoaded', function() {
        const sliderContainer = document.querySelector('.produkt-slider-container');
        const slides = sliderContainer.querySelectorAll('.produkt-slide');
        const prevButton = sliderContainer.querySelector('.slider-nav.prev');
        const nextButton = sliderContainer.querySelector('.slider-nav.next');
        const progressIndicator = sliderContainer.querySelector('.indicator-progress');
        let currentIndex = 0;
        const totalSlides = slides.length;

        // Funktion för att uppdatera slidern
        function updateSlider() {
            const slider = sliderContainer.querySelector('.produkt-slider');
            slider.style.transform = `translateX(-${currentIndex * 100}%)`;

            // Uppdatera indikatorns bredd
            const progressWidth = ((currentIndex + 1) / totalSlides) * 100;
            progressIndicator.style.width = `${progressWidth}%`;

            // Uppdatera aktiva klass
            slides.forEach((slide, index) => {
                slide.classList.toggle('active', index === currentIndex);
            });
        }

        // Hantera "Föregående"-knappen
        prevButton.addEventListener('click', () => {
            currentIndex = (currentIndex === 0) ? totalSlides - 1 : currentIndex - 1;
            updateSlider();
        });

        // Hantera "Nästa"-knappen
        nextButton.addEventListener('click', () => {
            currentIndex = (currentIndex + 1) % totalSlides;
            updateSlider();
        });

        // Initialisera slidern
        updateSlider();
    });
</script>


<style>
    .product-slider {
        max-width: 100%;
        margin-bottom: 30px;
    }

    .main-image-wrapper {
        position: relative;
        text-align: center;
    }

    .main-image-container {
        width: 100%;
        height: 700px;
        /* Justera efter behov */
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background-color: #fff;
        /* Valfri bakgrund vid tomrum */
        border-radius: 10px;
    }

    .main-image-container img {
        max-width: 100%;
        max-height: 700px;
        object-fit: contain;
        /* eller 'cover' om du hellre fyller hela rutan */
        transition: opacity 0.3s ease-in-out;
    }


    .slider-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: transparent;
        border: none;
        color: var(--accentColor);
        font-size: 24px;
        padding: 10px;
        cursor: pointer;
        z-index: 2;
    }

    .slider-arrow.prev {
        left: 10px;
    }

    .slider-arrow.next {
        right: 10px;
    }

    .thumbnail-wrapper {
        justify-self: center;
        display: flex;
        gap: 10px;
        margin-top: 10px;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .thumbnail {
        width: 100px;
        height: 90px !important;
        object-fit: cover;
        border-radius: 6px;
        border: 2px solid transparent;
        cursor: pointer;
        transition: border 0.3s;
    }

    .thumbnail.active {
        border: 2px solid var(--accentColor);
    }

    @media screen and (max-width: 992px) {
        .thumbnail {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: border 0.3s;
        }

        .main-image-container {
            height: 400px;
        }
    }
</style>