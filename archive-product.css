/* --- Global Bredd och Maxbredd --- */
.container,
.shop-loop-content-smort,
.category-header,
.category-description-container {
  width: 95vw;
  max-width: 2000px;
  margin: 0 auto;
}

/* --- Header Layout (60% text / 40% bild) --- */
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2% 0 2% 0;
  flex-wrap: nowrap;
}

/* Textdelen - 60% bredd */
.category-header-text {
  flex: 0 0 60%;
  max-width: 60%;
}

/* Bild/Videodel - 40% bredd */
.category-header-media {
  flex: 0 0 40%;
  max-width: 40%;
  position: relative;
}

/* Justering av bilder och video */
.category-image,
.video-thumbnail-container img {
  width: 100%;
  height: 400px !important;
  border-radius: 10px;
  object-fit: cover;
  margin-bottom: -150px;
}

/* Video-knapp */
.video-play-button {
  position: absolute;
  top: 80%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: var(--accentColor);
  color: #fff;
  font-size: 20px;
  padding: 15px;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.3s ease;
  width: 50px;
  height: 50px;
}

.video-play-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

/* --- Responsiv anpassning för mindre skärmar --- */
@media (max-width: 1024px) {
  .category-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .category-header-text,
  .category-header-media {
    flex: 0 0 100%;
    max-width: 100%;
  }
  .category-header-media {
    display: none;
  }
}

/* --- Produktkort Grid (Behåller 10px gap) --- */
.shop-loop-content-smort {
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
}

.products.columns-4 .product {
  width: calc(25% - 10px);
  margin: 0;
}

@media (max-width: 768px) {
  .products.columns-4 .product {
    width: calc(50% - 10px);
  }
}

h1.category-title {
  font-size: 4rem;
  margin-top: 20px;
  margin-bottom: 0px;
}
.category-intro p {
  font-size: 16px !important;
}
.category-intro {
  max-width: 90%;
  margin-top: 2%;
}
h2.filter-title {
  font-size: 3rem;
  margin-bottom: 10px;
  padding-top: 2%;
}
.shop-loop-outer {
  background-color: #f3f3f3;
  padding-bottom: 3%;
}

/* Produktkort */

/* --- Produktkort Styling --- */
.custom-product-card {
  width: 100%;
  list-style: none;
}

.product-card {
  display: flex;
  flex-direction: column;
  align-items: left;
  transition: transform 0.2s ease-in-out;
  text-align: center;
}

.product-card:hover {
  transform: translateY(-3px);
}

/* --- Produktbild --- */
.woocommerce ul.products li.product a img {
  height: 350px;
  object-fit: contain;
  padding: 0px;
  width: calc(100% - 0px);
  background-color: #fff;
}

/* --- Produktinformation --- */
.product-info {
  width: calc(100% - 20px);
  padding: 0px 10px;
}

/* --- Produktnamn --- */
h2.woocommerce-loop-product__title {
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 5px;
 max-width: 100% !important;
	margin-top: 15px !important;
}

/* --- Pris --- */
.woocommerce ul.products li.product .price {
  font-size: 24px;
  font-weight: bold;
  color: var(--accentColor3);
  margin-bottom: 10px;
  text-align: left;
  font-family: "CustomHeadingFont";
}

/* --- Lagerstatus --- */
.stock-status {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-bottom: 10px;
  justify-content: left;
  color: #000;
}
.title-stock-div {
  text-align: left;
}
/* --- Plupparna för lagerstatus --- */
.stock-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

/* --- Färger för lagerstatus --- */
.stock-green .stock-dot {
  background-color: #00cc44; /* Grön för "I lager" */
}

.stock-yellow .stock-dot {
  background-color: #ffcc00; /* Gul för "Restorder" */
}

.stock-red .stock-dot {
  background-color: #ff3333; /* Röd för "Slut i lager" */
}

/* --- Lägg i varukorg-knapp --- */
.woocommerce ul.products li.product .button {
  background: #ff6600;
  color: white;
  font-size: 14px;
  font-weight: bold;
  padding: 12px 15px;
  text-transform: uppercase;
  border-radius: 5px;
  width: 100%;
  text-align: center;
  transition: background 0.3s ease-in-out;
}

.woocommerce ul.products li.product .button:hover {
  background: #cc5200;
}

.woocommerce ul.products li.product a img {
  height: 350px;
  object-fit: contain;
  margin: 0px;
  width: calc(100% - 0px);
}
.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
  float: left;
  margin: 10px !important;
  padding: 0;
  position: relative;
  width: calc(25% - 20px);
  margin-left: 0;
}
.title-stock-div h2 {
    font-size: 1.3rem !important;
    color: #000;
    padding-top: 0px !important;
    line-height: 1.3;
    height: 50px;
}
a.buy-product-button {
  width: 97%;
  background-color: var(--accentColor);
  display: block;
  color: #fff;
  font-family: "CustomHeadingFont";
  font-size: 20px;
  padding: 5px 10px;
  text-transform: uppercase;
}

/* Läs mer / mindre funktion */

.category-description-container {
  position: relative;
  margin: 5% auto;
}

.category-description {
  max-height: 400px;
  overflow: hidden;
  position: relative;
  transition: max-height 0.3s ease-in-out;
}

.fade-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 1) 100%
  );
  pointer-events: none;
  transition: opacity 0.3s ease-in-out;
}

/* När beskrivningen expanderas */
.category-description-container.expanded .category-description {
  max-height: none;
}

.category-description-container.expanded .fade-overlay {
  opacity: 0;
}

.archive .fade-overlay {
  bottom: 10%;
}
