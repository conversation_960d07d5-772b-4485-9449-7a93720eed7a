/*
 Theme Name:   Smort Commerce Child
 Theme URI:    https://smort.se
 Description:  Smort Commerce Child Theme
 Author:       Smort AB
 Author URI:   https://smort.se
 Template:     smort_commerce
 Version:      1.0.0
*/

/* Import the parent theme's stylesheet */
@import url("../Smort_commerce/style.css");
@import url("https://fonts.googleapis.com/css2?family=Saira:ital,wght@0,100..900;1,100..900&display=swap");

/* Add your custom styles here */

/* Main */

/* Core */
:root {
  --textColor: #616573;
  --textColorLight: #fff;
  --textColorDark: rgb(136, 137, 145);
  --textColorHeadlines: var(--accentColor);

  --textSize: 18px;
  --textLineHeight: 160%;

  --fontFamily: "CustomHeadingFont", sans-serif;
  --fontFamilySecond: "Saira" !important;

  --arrowRight: url("/wp-content/themes/smort_commerce/img/arrow-up-righ-orange.svg");

  --accentColor: #ff5e00;
  --accentColor2: #000000;
  --accentColor3: #212121;
  --accentColor4: #fefff8;

  --buttonColorLight: #fff;
  --buttonColorDark: var(--accentColor2);
  --buttonTextLight: var(--textColorLight);
  --buttonTextDark: var(--textColorLight);

  --mainLetterSpacing: 2px;
}
article {
  margin-bottom: 0px;
}
body {
  font-family: var(--fontFamilySecond);
}
p {
  font-size: 20px;
  line-height: 1.7;
}
a:hover {
  color: var(--accentColor2);
}
a:active {
  color: var(--accentColor) !important;
}
a {
  text-decoration: none;
}
html {
  scroll-behavior: smooth;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  text-transform: uppercase;
}
/* Header */

.smort-header {
  background: #fff;
  width: 100%;
  z-index: 9999;
}
.smort-header .menu-item a {
  color: #fff;
}

@media only screen and (max-width: 768px) {
  .contact-overlaymenu a {
    width: 110px;
    font-size: 15px;
  }
}

/* Headings */

.hero-heading-xk {
  font-size: 7rem;
  margin: 0px;
  line-height: 1;
}
.main-heading-xk {
  font-size: 4.5rem;
  margin: 0px;
  margin-bottom: 0px !important;
}
.sub-heading-xk {
  font-size: 20px;
  text-transform: uppercase;
  margin-bottom: 10px;
}

/* Header */

.wc-timeline-button-show-cart.right {
  display: none !important;
}

.kb-submit-field .kt-btn-inner-text {
  font-family: "CustomHeadingFont";
  font-size: 20px;
}

.smort-header {
  padding: 10px 0px;
}
.flag-icon {
  height: 20px !important;
}

/* Buttons */

.outline-btn-white-xk a {
  background-color: transparent;
  border-radius: 0px;
  text-align: left;
  padding: 14px;
  min-width: 200px;
  border: 1px solid #fff;
  min-width: 240px;
  position: relative;
  font-family: var(--fontFamily);
  color: #fff;
  text-transform: uppercase;
}
.outline-btn-white-xk a:after {
  content: var(--arrowRight);
  position: absolute;
  right: 9px;
  top: 13px;
  transition: transform 0.2s ease-in-out;
}

.outline-btn-black-xk a {
  background-color: transparent;
  border-radius: 0px;
  text-align: left;
  padding: 14px;
  min-width: 200px;
  border: 1px solid #000000;
  min-width: 240px;
  position: relative;
  font-family: var(--fontFamily);
  color: #000;
  text-transform: uppercase;
}
.outline-btn-black-xk a:after {
  content: var(--arrowRight);
  position: absolute;
  right: 9px;
  top: 13px;
}

.main-btn-xk a {
  background-color: var(--accentColor);
  border-radius: 0px;
  text-align: left;
  padding: 10px;
  min-width: 200px;
  border: 1px solid var(--accentColor);
  min-width: 240px;
  position: relative;
  font-family: var(--fontFamily);
}
.main-btn-xk a:after {
  content: var(--arrowRight);
  position: absolute;
  right: 9px;
  filter: brightness(0) invert(1);
  top: 13px;
  transition: transform 0.2s ease-in-out;
}

.cta-row-btn a {
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
  font-size: 17px !important;
  padding: 5px 30px;
}

a.wp-block-button__link:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease-in-out;
}

/* General */

.hero-text {
  font-size: 30px;
  max-width: 400px;
  margin: 0px;
}
button#wc_search_trigger {
  padding: 12px;
}

button#wc_search_trigger {
  border-radius: 30px;
  border: 0px;
}

.smort-topbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 20px;
  background-color: #f7f7f7;
  font-family: sans-serif;
  flex-wrap: wrap;
}

.smort-topbar .topbar-left,
.smort-topbar .topbar-right {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.smort-topbar .usp-header {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.smort-topbar .usp-header li {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: clamp(10px, 1.2vw, 10px);
  white-space: nowrap;
  color: #fff;
}

.smort-topbar .usp-header li img {
  width: 14px;
  height: auto;
}

/* Responsivt: stapla på mindre skärmar */
@media (max-width: 768px) {
  .smort-topbar {
    flex-direction: column;
    align-items: flex-start;
  }

  .smort-topbar .topbar-left,
  .smort-topbar .topbar-right {
    width: 100%;
    justify-content: flex-start;
  }

  .smort-topbar .usp-header {
    flex-direction: column;
    gap: 5px;
  }

  .smort-topbar .usp-header li {
    font-size: 14px;
    white-space: normal;
  }

  .smort-topbar .topbar-right {
    justify-content: space-between;
    margin-top: 10px;
  }
}

.usp-header img {
  display: block;
  max-height: 15px;
  margin-right: 0px;
}
a.af-login {
  color: #fff;
  padding: 4px 33px;
  font-size: 13px;
  background-color: var(--accentColor3);
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  border: 1px solid;
}

/* Blend mode section */

.mixed-blend-mode-row {
  background-image: url("http://xenonkungen.local/wp-content/uploads/2025/01/drone-xk.jpg") !important;
  background-size: cover;
  background-position: center center;
  background-attachment: scroll;
  background-repeat: no-repeat;
  position: relative;
  isolation: isolate !important; /* Nödvändigt för blend mode att fungera korrekt */
}

.big-heading-xk {
  color: #ffffff; /* Vit färg på texten */
  mix-blend-mode: difference; /* Blend mode för att skapa effekten */
  font-size: 7rem; /* Exempelstorlek, anpassa vid behov */
  line-height: 1;
  text-align: center; /* Centrerar texten */
  margin: 0;
  position: relative; /* Behåller texten inom rätt position */
  z-index: 1; /* Texten måste ligga ovanför bakgrunden */
}
.big-heading-xk {
  color: #ffffff; /* Textens grundfärg */
  mix-blend-mode: difference; /* Blandningsläge */
  font-size: 7rem;
  line-height: 1;
  text-align: center;
  position: relative;
  z-index: 1; /* Se till att texten ligger ovanpå bakgrunden */
}

/* Responsive generellt */

@media screen and (max-width: 992px) {
  .hero-heading-xk {
    font-size: 4.5rem;
    margin-bottom: 0px;
  }
  .hero-text {
    text-align: left;
  }
  .main-heading-xk {
    font-size: 3rem;
  }
  .cta-row-btn a {
    margin-top: 10px;
  }
}

/* WooCommerce styling */

nav.woocommerce-breadcrumb {
  background-color: #000;
  text-align: center;
  padding: 10px !important;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
  font-size: 18px !important;
}
nav.woocommerce-breadcrumb a {
  color: #fff !important;
}

/* Footer */

.footer-link-row a {
  position: relative;
  text-decoration: none;
  color: inherit; /* Behåller textfärgen */
  padding-bottom: 5px; /* Avstånd för understrykningen */
  font-size: 20px;
  margin: 0px;
}
.footer-link-row h3 {
  margin: 0px 0px 12px 0px;
}
.footer-link-row a::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px; /* Tjockleken på linjen */
  background-color: currentColor; /* Använder textens färg */
  transition: width 0.3s ease-in-out;
}

.footer-link-row a:hover::after {
  width: 100%; /* Expanderar linjen vid hover */
}

.hero-section-xk::after {
  content: "";
  position: absolute;
  bottom: 50px; /* Justera så den är under diven */
  left: 50%;
  transform: translateX(-50%);
  width: 30px; /* Anpassa storleken efter behov */
  height: 30px;
  background-image: url("/wp-content/uploads/2025/02/arrow-jumping.png");
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: jumpAnimation 1.5s infinite ease-in-out;
}

@media screen and (max-width: 768px) {
  .hero-section-xk::after {
    bottom: 35px;
  }
}

@keyframes jumpAnimation {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(10px);
  }
}

/* Anställda */

/* Grid container */
.anstallda-grid-container {
  display: grid;
  grid-template-columns: repeat(4, 25%);
  gap: 20px;
}

/* Individuell anställd post */
.anstalld-item {
  background-color: #ffffff;
  padding: 20px;
  color: #000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-radius: 0px;
}

/* Bild */
.anstalld-bild {
  width: 100%;
  background-size: cover;
  background-position: center;
  margin-bottom: 20px;
}

/* Namn */
.anstalld-namn {
  font-size: 2rem;
  color: #000;
  margin-bottom: 10px;
  margin-top: 0px;
}

/* Roll */
.anstalld-roll {
  font-size: 18px;
  color: #000;
  margin-bottom: 0px;
  margin-top: 0px;
}

/* Kontaktuppgifter */
.anstalld-kontakt a {
  color: #000;
  text-decoration: none;
  display: block;
}

.anstalld-kontakt a:hover {
  color: #000; /* Ljusblå nyans för hover */
}

.anstalld-kontakt a {
  color: var(--accentColor);
}
.anstalld-kontakt p {
  margin: 0px;
}
.anstalld-kontakt {
  margin-top: 10px;
}

@media screen and (max-width: 992px) {
  .anstallda-grid-container {
    display: grid;
    grid-template-columns: repeat(1, 100%);
    gap: 20px;
  }
}

.wp-block-separator {
  border: none;
  border-top: 1px solid #000;
  opacity: 1 !important;
  width: 100%;
}

.kb-splide .splide__pagination__page {
  width: 20px;
  border-radius: 5px;
}
.kb-splide .splide__pagination__page.is-active {
  background-color: var(--accentColor);
}

.wp-block-image.burger-menu {
  max-width: 120px;
}

/* Ambassadörer CSS */

.ambassador-link {
  display: block;
  text-decoration: none;
  color: inherit;
}

/* Ambassadörer Grid */
.ambassadors-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
}

/* Ambassadör Item */
.ambassador-item {
  position: relative;
  min-height: 550px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  overflow: hidden;
  border-radius: 10px;
}

/* Mörkt overlay */
.ambassador-item .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
}

/* Textinnehåll */
.ambassador-item .content {
  position: relative;
  z-index: 2;
  color: #fff;
  padding: 20px;
}

.ambassador-item h2 {
  font-size: 3rem;
  margin-bottom: 10px;
}

/* Läs mer-knapp */
.ambassador-item .read-more-btn {
  display: inline-block;
  background: var(--accentColor);
  color: #fff;
  padding: 10px 20px;
  text-decoration: none;
  font-weight: bold;
  border-radius: 0px;
  transition: background 0.3s ease;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}

/* Anpassning för mindre skärmar */
@media (max-width: 1024px) {
  .ambassadors-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .ambassadors-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

.ambassador-item:hover {
  transform: scale(0.95);
  transition-duration: 0.5s;
}

/* Mitt konto */

/* Grundläggande styling för WooCommerce Mitt Konto */
.woocommerce-MyAccount-navigation {
  display: flex;
  justify-content: center;
  gap: 15px;
  background: var(--accentColor2);
  padding: 15px;
  border-radius: 0px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  width: 100% !important;
}

@media (max-width: 768px) {
  .woocommerce-MyAccount-navigation {
    width: 95% !important;
  }
}

/* Navigationsobjekt som horisontell meny */
.woocommerce-MyAccount-navigation ul {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

.woocommerce-MyAccount-navigation ul li {
  margin: 0;
}

.woocommerce-MyAccount-navigation ul li a {
  display: block;
  padding: 12px 20px;
  text-decoration: none;
  color: #fff;
  font-weight: 600;
  border-radius: 0px;
  transition: all 0.3s ease-in-out;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  font-size: 22px;
}

/* Hover-effekt */
.woocommerce-MyAccount-navigation ul li a:hover {
  background: var(--accentColor);
  color: var(--accentColor4);
}

/* Aktiv länk */
.woocommerce-MyAccount-navigation ul li.is-active a {
  background: var(--accentColor);
  color: var(--accentColor4);
}

/* Innehållsstyling */
.woocommerce-MyAccount-content {
  padding-top: 30px;
  border-radius: 0px;
  margin-top: 20px;
  color: var(--accentColor3);
  font-size: 16px;
  width: 100% !important;
  max-width: 1200px;
  float: unset !important;
  margin: 5% auto;
}

@media (max-width: 768px) {
  .woocommerce-MyAccount-content {
padding: 0 1rem;
width: 95% !important;
  }
}

/* Anpassning för knappar */
.woocommerce-Button,
.woocommerce-button {
  background: var(--accentColor);
  color: var(--accentColor4);
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
  border: none;
  transition: all 0.3s ease-in-out;
}

.woocommerce-Button:hover,
.woocommerce-button:hover {
  background: var(--accentColor2);
  color: var(--accentColor4);
}

/* Mobilanpassning */
@media (max-width: 768px) {
  .woocommerce-MyAccount-navigation {
    flex-direction: column;
    align-items: center;
  }

  .woocommerce-MyAccount-navigation ul {
    flex-direction: column;
    width: 100%;
  }

  .woocommerce-MyAccount-navigation ul li {
    width: 100%;
  }

  @media (max-width: 768px) {
    .woocommerce-MyAccount-navigation ul li a {
    width: 93% !important;
    }
  }

  .woocommerce-MyAccount-navigation ul li a {
    width: 100%;
    text-align: center;
  }
}

.my-account-dashboard {
  text-align: center;
  margin-bottom: 30px;
}

.my-account-dashboard h2 {
  font-size: 3rem;
  color: var(--accentColor3);
}

.my-account-dashboard p {
  font-size: 20px;
  color: var(--accentColor3);
  margin-bottom: 20px;
  margin: 0 auto;
  margin-bottom: 40px;
  max-width: 1000px;
}

.dashboard-cards {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

.dashboard-card {
  background: #fff;
  border: 1px solid #f9f9f9;
  border-radius: 0;
  padding: 20px;
  width: 30%;
  min-height: 250px;
  text-align: center;
  text-decoration: none;
  color: var(--accentColor3);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
}

.dashboard-icon {
  font-size: 30px;
  color: var(--accentColor);
  margin-bottom: 10px;
}

.dashboard-card h3 {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

/* Responsiv design */
@media (max-width: 768px) {
  .dashboard-cards {
    flex-direction: column;
    align-items: center;
  }

  .dashboard-card {
    width: 90%;
  }
}

.woocommerce-account .page-content {
  background-image: url(/wp-content/uploads/2025/02/layers-xk.svg);
  background-position: center;
}

.woocommerce-info,
.woocommerce-error {
  border-top: 0px;
  background-color: #000;
  text-align: center;
  color: #fff;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  font-size: 1.2rem;
}
.woocommerce-info::before {
  display: none;
}

.woocommerce .woocommerce-info .button {
  color: #fff;
  background-color: transparent;
  border: 2px solid;
  border-radius: 0px !important;
  padding: 8px 20px;
  font-size: 12px;
}
.woocommerce-info::after {
  display: none;
}

/* Thank you page */

.woocommerce-order {
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 5%;
  padding-bottom: 5%;
}
p.woocommerce-notice.woocommerce-notice--success.woocommerce-thankyou-order-received {
  text-align: center;
  margin-bottom: 50px;
  font-family: "CustomHeadingFont";
  font-size: 2.5rem;
  text-transform: uppercase;
}
.woocommerce ul.order_details {
  margin: 0 0 3em;
  list-style: none;
  text-align: left;
  margin: 0 auto;
  max-width: 1000px;
  margin-bottom: 50px;
}
h2.woocommerce-order-details__title {
  font-size: 2rem;
  text-align: center;
}
.woocommerce .woocommerce-customer-details .woocommerce-column__title {
  margin-top: 0;
  text-align: center;
  font-size: 2rem;
}
td.woocommerce-table__product-name.product-name a {
  color: var(--accentColor);
}
.woocommerce .woocommerce-customer-details :last-child {
  background-color: #fff;
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
  border: 0px;
}
.woocommerce-order-received .page-content {
  background-color: #f9f9f9;
}

/* Tabell ordrar */

/* Ta bort borders och skapa en ren look */
.woocommerce-orders-table {
  border-collapse: separate;
  border-spacing: 0 10px; /* 10px avstånd mellan rader */
  width: 100%;
}

/* Ta bort borders från tabellhuvudet */
.woocommerce-orders-table thead {
  font-weight: bold;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  font-size: 22px;
}

/* Göra varje rad till en "kort-liknande" stil */
.woocommerce-orders-table tbody tr {
  background: #fff;
  border-radius: 0px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease-in-out;
}

/* Lätt hover-effekt för att göra det mer interaktivt */
.woocommerce-orders-table tbody tr:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Padding för att ge mer luft mellan innehållet */
.woocommerce-orders-table td,
.woocommerce-orders-table th {
  padding: 15px;
  text-align: left;
}

/* Ändra länkfärger till din orange accentfärg */
.woocommerce-orders-table a {
  color: var(--accentColor);
  font-weight: 600;
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

.woocommerce-orders-table a:hover {
  color: var(--accentColor2);
}

/* Åtgärdsknappar styling */
.woocommerce-orders-table__cell-order-actions .woocommerce-button {
  background: var(--accentColor);
  color: var(--accentColor4);
  padding: 10px 15px;
  border-radius: 6px;
  font-weight: bold;
  text-transform: uppercase;
  transition: background 0.3s ease-in-out;
}



.woocommerce-orders-table__cell-order-actions .woocommerce-button:hover {
  background: var(--accentColor2);
}

/* Gör tabellen responsiv */
@media (max-width: 768px) {
  .woocommerce-orders-table thead {
    display: none;
  }

  .woocommerce-orders-table tbody tr {
    display: block;
    margin-bottom: 10px;
    padding: 15px;
  }

  .woocommerce-orders-table td {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid #eee;
  }

  .woocommerce-orders-table td:last-child {
    border-bottom: none;
  }

  .woocommerce-orders-table td::before {
    content: attr(data-title) ": ";
    font-weight: bold;
    color: var(--accentColor3);
  }
}

.woocommerce table.shop_table {
  border: 0px;
}
.woocommerce table.my_account_orders td {
  padding: 50px 10px;
}
.woocommerce table.my_account_orders .button {
  white-space: nowrap;
  background-color: var(--accentColor);
  color: #fff;
  font-family: "CustomHeadingFont";
  font-size: 22px;
  padding: 5px 30px;
  text-align: center;
  max-width: 120px;
}

.order-again {
background-color: #fff !important;
  margin-left: 0.8rem !important;
  border: 1px solid var(--accentColor2) !important;
  color: var(--accentColor2) !important;
  font-weight: 500 !important;
}


.woocommerce-view-order .page-content {
  text-align: center;
}
.cta-orders-div {
  text-align: center;
  margin-top: 5%;
}
h2.order-cta-title {
  font-size: 3rem;
}
.cta-orders-div a {
  background-color: var(--accentColor);
  color: #fff;
  padding: 5px 30px;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  font-size: 1.2rem;
}

.woocommerce .col2-set .col-1,
.woocommerce-page .col2-set .col-1,
.woocommerce .col2-set .col-2,
.woocommerce-page .col2-set .col-2 {
  width: calc(48% - 40px);
  background-color: #f9f9f9;
  padding: 20px;
}
.woocommerce-Address a {
  color: var(--accentColor);
}
.woocommerce form .form-row input.input-text,
.woocommerce form .form-row textarea {
  border: 0px;
  min-height: 40px;
  border: 1px solid #e5e5e5;
  padding-left: 10px;
}
.woocommerce form .form-row label {
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}
.woocommerce form .show-password-input,
.woocommerce-page form .show-password-input {
  position: absolute;
  top: 0.3em !important;
}
button.woocommerce-Button.button,
button.button {
  background-color: var(--accentColor) !important;
  color: #fff !important;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  font-size: 22px;
}
.select2-container--default
  .select2-results__option--highlighted[aria-selected],
.select2-container--default
  .select2-results__option--highlighted[data-selected] {
  background-color: var(--accentColor) !important;
  color: #fff;
}
.woocommerce-address-fields button {
  background-color: var(--accentColor) !important;
  color: #fff !important;
  min-width: 200px;
  border-radius: 0px;
}

/* Reviews section */

h2.reviews-title {
  font-size: 4.5rem;
  line-height: 1;
  margin: 50px 0px;
  text-align: center;
  color: #000;
}
.reviews-section-ks {
  background-color: #f2f2f2;
  padding: 3% 0%;
  margin-top: 5%;
}
.inner-section-reviews {
  max-width: 90vw;
  margin: 0 auto;
}
.cr-all-reviews-shortcode .cr-summaryBox-wrap,
.cr-reviews-grid .cr-summaryBox-wrap {
  margin: 0;
  background-color: #ffffff;
}

.ivole-meter .ivole-meter-bar {
  border-radius: 1px;
  background: var(--accentColor);
  background: -webkit-linear-gradient(
    top,
    var(--accentColor),
    var(--accentColor)
  );
  background: linear-gradient(
    to bottom,
    var(--accentColor),
    var(--accentColor)
  );
  background-color: var(--accentColor);
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0), inset 0 -1px 0 rgba(0, 0, 0, 0);
  -webkit-transition: width 0.5s ease;
  transition: width 0.5s ease;
  float: left;
  font-size: 0;
  height: 100%;
  width: 0;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.cr-all-reviews-shortcode
  .cr-summaryBox-wrap
  .cr-add-review-wrap
  .cr-all-reviews-add-review,
.cr-reviews-grid
  .cr-summaryBox-wrap
  .cr-add-review-wrap
  .cr-all-reviews-add-review {
  background-color: var(--accentColor);
  color: #fff;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}
.cr-ajax-search,
.cr-all-reviews-shortcode .cr-count-row {
  display: none !important;
}
.cr-all-reviews-shortcode ol li.review .comment_container {
  background-color: #fff;
  padding: 15px;
  border-bottom: 0px;
  margin-top: 10px;
}
.cr-all-reviews-shortcode ol.commentlist li .comment-text {
  border-bottom: 0px;
}
.cr-all-reviews-shortcode ol li.review .comment_container img.avatar {
  top: 10px;
  left: 10px;
}
.cr-all-reviews-shortcode ol li.comment .comment_container .cr-avatar-check,
.cr-all-reviews-shortcode ol li.review .comment_container .cr-avatar-check {
  left: 44px;
  top: 38px;
}
span.woocommerce-review__author {
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
  font-size: 20px !important;
}

.woocommerce .star-rating span::before {
  color: var(--accentColor);
}
.woocommerce div.product .woocommerce-product-rating {
  margin-bottom: 10px;
}
a.woocommerce-review-link {
  color: #000;
}

.cr-all-reviews-shortcode ol.commentlist li {
  margin-bottom: 5px;
}
.woocommerce ul.products li.product .star-rating {
  display: none;
}

/* Kassa & Varukorg */

.wc-timeline-product {
  width: calc(100% - 20px) !important;
  padding: 10px 25px;
  margin: 10px;
  background-color: #fff;
}
.wc-items-container {
  background-color: #f9f9f9;
}
.wc-timeline-remove-product {
  cursor: pointer;
  font-size: 15px;
  height: 25px;
  width: 25px;
  border-radius: 50%;
  line-height: 25px;
  background-color: var(--accentColor);
  color: #fff !important;
}
.wc-timeline-product-price,
.wc-timeline-product .currency {
  font-weight: bold;
  font-size: 1.5rem !important;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
}
.wc-timeline-container-close-icon {
  font-size: 30px;
  border-radius: 50%;
  font-family: Arial;
  cursor: pointer;
  width: 90px;
  height: 100%;
  line-height: 90px;
  color: var(--upsell-modal-close-text);
  background-color: var(--accentColor) !important;
}
.wc-timeline-empty-modal {
  background-color: transparent !important;
}
.wc-timeline-empty-modal .wc-timeline-button,
.wc-timeline-checkout-button,
.wc-timeline-empty-modal .wc-timeline-button,
.wc-timeline-checkout-button {
  background-color: var(--accentColor) !important;
  font-family: "CustomHeadingFont";
  font-size: 20px !important;
}
.wc-timeline-product-add a {
  font-family: "CustomHeadingFont";
  font-size: 1.2rem !important;
  text-transform: uppercase;
  border-radius: 0px !important;
}
.wc-timeline-empty-modal .wc-timeline-button,
.wc-timeline-checkout-button {
  font-size: 30px;
}
.wc-timeline-product-title {
  font-family: "CustomHeadingFont";
  font-size: 20px;
  text-transform: uppercase;
  margin: 10px 0px;
}
.woocommerce-checkout .page-content {
  max-width: 90vw;
  margin: 0 auto;
  padding-top: 2%;
  padding-bottom: 5%;
}

ul.woocommerce-error {
  display: none;
}

/* Kassa */

.woocommerce-checkout input.cart-quantity {
  font-size: 20px;
  font-family: "CustomHeadingFont";
  font-weight: 800;
  text-align: center;
  width: 30px;
  background-color: transparent;
  border: 0;
}
.woocommerce-checkout .woocommerce-info {
  background-color: #fff;
  border: 1px solid var(--accentColor4);
  margin-bottom: 0px;
  color: #000;
}
a.showcoupon {
  color: var(--accentColor);
  margin-left: 10px;
}
.totals-line {
  background-color: #f9f9f9;
}
.totals-line {
  background-color: #f9f9f9;
  margin: 10px 0px;
  padding: 10px 10px;
}
.checkout-totals {
  padding: 0px !important;
}
.checkout-totals .totals-line:nth-of-type(2) {
  background-color: #00800012;
}
.woocommerce form.checkout_coupon,
.woocommerce form.login,
.woocommerce form.register {
  border: 1px solid #cfc8d8;
  padding: 20px;
  margin: 2em 0;
  text-align: left;
  border-radius: 5px;
  background-color: #000;
  color: #fff;
}

h3.upsell-title,
.woocommerce-additional-fields h3 {
  font-size: 2rem;
  margin: 20px 0px 15px;
}
p#order_comments_field {
  display: none;
}
.wc-timeline-product.upsell {
  box-shadow: 0 0.4rem 3rem rgba(0, 0, 0, 0) !important;
  border: 1px solid #cccccc;
}

.prdctfltr_wc_widget.prdctfltr_wc .prdctfltr_filter_inner {
  overflow: visible;
  display: flex;
  flex-wrap: wrap;
}
.prdctfltr_columns_1 .prdctfltr_filter {
  width: 20% !important;
}
.prdctfltr_terms_customized_select .prdctfltr_widget_title,
.pf_default_select .prdctfltr_widget_title {
  cursor: pointer;
  display: block;
  padding: 10px;
  background-color: #fff;
  border: 1px solid;
}
h2.widget-title {
  margin: 0px !important;
  font-size: 18px !important;
}
.prdctfltr_filter label {
  display: block;
  font-size: 16px !important;
  line-height: 40px !important;
  border-bottom: 1px solid #d4d4d4;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
}
.prdctfltr_checkboxes .prdctfltr_sub {
  margin-left: 1.5em !important;
}
.prdctfltr_customize_count {
  background-color: var(--accentColor) !important;
  font-size: 13px !important;
  color: #fff !important;
  padding: 2px 8px !important;
  margin-left: 10px !important;
  border-radius: 50% !important;
}

.prdctfltr_collector_flat > span {
  background-color: #f4f4f4;
  background-color: var(--accentColor) !important;
  border-radius: 0px !important;
  color: #fff !important;
}
.prdctfltr-delete:before {
  content: "\f002";
  color: #fff;
}
.cart-item-price {
  font-family: "CustomHeadingFont";
  font-size: 18px;
  color: var(--accentColor);
}

/* inloggning */

/* Centrera inloggningsformuläret */
.woocommerce-form-login,
.woocommerce-form-register {
  max-width: 800px;
  margin: 50px auto;
  padding: 30px;
  background: #fff;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

/* Gör inmatningsfälten snyggare */
.woocommerce-form-row input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

/* Highlight på inputfält vid fokus */
.woocommerce-form-row input:focus {
  border-color: var(--accentColor, #388349);
  outline: none;
}

/* Justera checkboxens utseende */
.woocommerce-form__label-for-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  margin-top: 10px;
}

/* Anpassad styling för knappar */
.woocommerce-button {
  width: 100%;
  padding: 12px;
  background: var(--accentColor, #388349);
  color: white;
  font-size: 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease;
}

/* Effekt vid hover */
.woocommerce-button:hover {
  background: #2f6b3e;
}

/* Länk till glömt lösenord */
.woocommerce-LostPassword a {
  display: inline-block;
  margin-top: 10px;
  color: var(--accentColor, #388349);
  text-decoration: none;
  font-weight: bold;
}

.woocommerce-LostPassword a:hover {
  text-decoration: underline;
}

/* Gör registrerings- och inloggningsformulär lika stora */
.u-columns {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.u-columns .u-column1,
.u-columns .u-column2 {
  flex: 1;
  background: #fff;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}

/* Anpassa rubriker */

form.woocommerce-form.woocommerce-form-login.login {
  margin: 0 auto;
}
h2.login-title {
  font-size: 3rem;
  text-align: center;
}
p.login-text {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  margin-bottom: 50px;
}
form.woocommerce-form.woocommerce-form-login.login {
  margin: 0 auto;
  margin-bottom: 5%;
}
.tax-product_brand img.category-image {
  object-fit: contain !important;
  background: #fff;
}
.smort-megamenu-right img {
  width: 100%;
  height: 500px !important;
  border: 1px solid #bcbcbc;
}
span.prdctfltr_regular_title {
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  font-size: 20px;
}
.prdctfltr_woocommerce.prdctfltr_wc.prdctfltr_always_visible
  .prdctfltr_woocommerce_ordering {
  padding: 10px;
}
.prdctfltr_count {
  font-weight: 600;
  background-color: var(--accentColor);
  color: #fff;
  border: 0px;
  font-size: 12px;
}
.ivole-meter {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0), inset 0 0 0 1px rgba(0, 0, 0, 0);
}
.smort-megamenu-left {
  max-height: 500px;
  overflow-y: scroll;
}
a.main-btn-xk {
  font-family: "CustomHeadingFont";
  font-size: 20px;
  background-color: var(--accentColor);
  padding: 5px 40px;
  color: #fff;
}
.right-sticky {
  justify-content: end;
}

/* Login knapp */
.woocommerce .woocommerce-form-login .woocommerce-form-login__submit {
  background-color: var(--accentColor) !important;
  padding: 12px 20px !important;
  color: #fff !important;
}

/*Woocommerce share */
.woocommerce-share {
  margin-top: 5px;
  align-items: center;
}

.woocommerce-share .share-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  margin-right: 10px;
  color: #000;
  margin-top: 15px;
}

.woocommerce-share a {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  text-decoration: none;
  color: #fff;
  font-size: 14px;
  margin-right: 10px;
  transition: color 0.3s ease;
  background: var(--accentColor);
  padding: 5px;
  max-height: 40px;
  width: 20px;
  justify-content: center;
}

.woocommerce-share a:hover {
  color: #000;
}

.woocommerce-share i {
  font-size: 20px;
}

/* Toggle TAX dropdown */
form.smort-tax-toggle-form {
  display: inline-block;
  margin: 0;
  margin-right: 12px;
  padding: 0;
}

form.smort-tax-toggle-form select {
  padding: 2px;
  font-size: 13px;
  background-color: #212121;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100px;
  font-family: "CustomContentFont";
}

form.smort-tax-toggle-form select:hover {
  background-color: #212121;
  border-color: #0073aa;
}

form.smort-tax-toggle-form select:focus {
  outline: none;
  border-color: #0073aa;
  border: 1px solid var(--accentColor);
  background-color: #212121;
}

form.smort-tax-toggle-form select option {
  padding: 10px;
  font-size: 14px;
}

.tax-toggle-div {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;

}

@media screen and (max-width: 992px) {
  .tax-toggle-div {
    display: flex;
  }
}

.tax-toggle-div span {
  font-size: 12px;
}

@media screen and (max-width: 768px) {
  .tax-toggle-div {
    margin-bottom: 2rem;
  }

  .tax-toggle-div span {
    font-size: 17px;
  }

  form.smort-tax-toggle-form select {
    padding: 10px 2px;
    width: 120px;
    font-size: 16px;
  }
}

.topbar-right {
  display: flex;
  align-items: center;
}

.tax-label {
  font-family: "CustomContentFont";
  font-size: 12px;
}

.woocommerce-share {
  margin-top: 10px;
  display: flex;
  border-top: 1px solid #dfdfdf;
}

/* Bundle custom */

.woosb-products .woosb-product .woosb-title .woosb-title-inner a {
  color: #000;
  line-height: 2;
}
.woosb-products .woosb-product .woosb-title .stock.in-stock {
  color: green;
  display: none;
}

.archive button.smort-heart-btn {
  position: absolute !important;
  right: 10px;
  top: 10px;
}
.wc-search-close {
  position: absolute;
  top: 50%;
  right: 70px;
  transform: translateY(-50%);
  font-size: 25px;
  font-weight: bold;
  color: #ffffff;
  cursor: pointer;
  background-color: var(--accentColor);
  padding: 6px 15px 10px 15px !important;
  line-height: 1;
  border-radius: 50%;
}


.product-carousel {
  padding: 0px;
  overflow: hidden;
}

.swiper-wrapper {
  display: flex;
}

.product-slide {
  border-radius: 00px;
}

.product-category {
  margin-top: 5px;
  font-size: 0.9em;
  color: #555;
}

.price {
  margin-top: 0px;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  gap: 5px;
  padding-bottom: 5px;
}

.cta-div {
  padding-bottom: 20px;
}
/* Swiper navigation buttons */

/* Wrapper för navigationspilar */
/* Föräldrakontainer som håller både swiper och navigation */
.swiper-carousel-wrapper {
  position: relative; /* Viktigt för att pilarna ska placeras rätt */
  overflow: hidden; /* Behåll hidden här för att förhindra sidescroll */
}

/* Navigeringspilar */
.swiper-navigation {
  position: absolute;
  width: 100%; /* Full bredd för att centrera pilarna */
  bottom: -40px; /* Placera nedanför karusellen */
  display: flex;
  justify-content: center; /* Centrera pilarna horisontellt */
  z-index: 10; /* Gör så att de syns ovanpå */
}

.swiper-button-next,
.swiper-button-prev {
  position: relative !important;
  color: #ff6600 !important;
  border: 1px solid #ff6600 !important;
  border-radius: 0px !important;
  width: 50px !important;
  height: 50px  !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 16px !important;
  margin: 0 10px !important;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2) !important;
  top: 0 !important;
  left: none !important;
}

/* Hover-effekt för pilar */
.swiper-button-next:hover,
.swiper-button-prev:hover {
  background: #ff6600;
  color: #fff;
  border-color: #ff6600;
}

.pagination-wrapper {
  display: flex !important;
  justify-content: center !important;
  margin-top: 20px !important;
  gap: 10px !important;
}
.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 15px !important;
}

/* titles */
h2.woocommerce-loop-product__title {
  font-size: 15px;
  color: #000;
  line-height: 1.5;
  margin-top: 0px;
  max-width: 80%;
  display: block;
  margin-bottom: 0px;
}
.product-category a {
  color: var(--accentColor3);
  margin-bottom: 0px;
  display: block;
}
.product-slide .amount {
  font-size: 20px;
  margin-top: 15px !important;
  display: block;
  margin-top: 0px !important;
}
.swiper-slide span.price bdi {
  font-size: 25px;
  text-decoration: none;
  text-transform: uppercase;
  font-family: "CustomHeadingFont";
}

span.product-parent-category {
  font-size: 12px;
  color: #000;
}

/* Dots */

/* Swiper pagination dots */
.swiper-pagination-bullet {
  background: #333;
  opacity: 0.7;
}

.swiper-pagination-bullet-active {
  background: #4c8077; /* Active dot color */
  opacity: 1;
}

/* Adjust the position and appearance of the dots if needed */
.swiper-pagination {
  margin-top: 15px;
  text-align: center;
  bottom: -30px !important;
}
.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
  width: 20px;
  border-radius: 5px;
}
.product-varumarke {
  color: #000;
  margin-top: 10px;
}
span.onsale {
  position: absolute;
  top: 20px;
  right: 20px;
  color: #ffffff;
  background-color: var(--accentColor);
  /* width: 50px; */
  /* height: 50px; */
  border-radius: 30px;
  padding: 10px 10px;
  font-size: 15px;
}
.price del bdi {
  color: #8e8e8e;
  font-size: 19px;
}

/* Ensure the star rating container has the correct display and positioning */
.woocommerce .star-rating {
  display: inline-block;
  position: relative;
  font-size: 1.2em; /* Adjust as necessary */
  color: #ffcc00; /* Color for the filled stars */
  line-height: 1; /* Make sure the stars are aligned */
  z-index: 10; /* Ensure the stars are above other elements */
}

/* Unfilled star background */
.woocommerce .star-rating::before {
  content: "★★★★★"; /* Five unfilled stars */
  color: #ccc; /* Color for unfilled stars */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1; /* Ensure unfilled stars are below the filled stars */
}

/* Filled stars */
.woocommerce .star-rating span {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  overflow: hidden;
  color: #ffcc00; /* Color for filled stars */
  z-index: 2; /* Ensure filled stars are above the unfilled stars */
}

/* Ensure the filled stars' width reflects the rating */
.woocommerce .star-rating span::before {
  content: "★★★★★"; /* Five filled stars */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3; /* Ensure filled stars are above the unfilled stars */
}

/* Ensure the star rating div has the proper z-index */
.woocommerce-loop-rating {
  position: relative;
  z-index: 9999; /* High z-index to ensure it's above other elements */
}

/* Ensure parent containers don't hide overflow */
.swiper-slide,
.product-slide {
  overflow: visible; /* Ensure that overflow isn't cutting off content */
}

/* Swiper slide */

.swiper-slide {
  height: auto;
  display: flex;
  flex-direction: column;
}
.swiper-wrapper {
  display: flex;
  align-items: stretch;
}
a.product-slider-cta {
  text-align: center !important;
  display: block;
  background-color: var(--accentColor);
  color: #fff;
  font-family: "CustomHeadingFont";
  text-transform: uppercase;
  padding: 10px;
  width: calc(100% - 12px);
  margin-top: 15px;
}
.swiper-slide h2.woocommerce-loop-product__title {
  max-width: 100%;
  font-size: 1.7rem;
  padding-top: 20px;
}
.swiper-slide span.price ins {
  text-decoration: none;
}

.product-slider-cta img {
  width: 50px;
  height: 50px;
  object-fit: contain;
  margin: 0px;
  border: 0px !important;
}

.product-slide img {
  width: calc(100% - 20px);
  height: 400px;
  object-fit: contain;
  margin: 10px;
  border-radius: 0px !important;
  background-color: #fff;
  border: 1px solid #f1f1f1;
}
.product-slide img.placeholder {
  object-position: bottom !important;
}

/* Arrow */

img.cta-arrow {
  width: 20px;
  height: 20px;
  margin: 0px;
  margin-top: 5px;
}

/* Container för produktbilder */
.product-image-container {
  position: relative;
  width: 100%;
  height: 320px;
  z-index: 1; 
}

.product-image-container .first-image,
.product-image-container .hover-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease-in-out;
  z-index: 1;
}



.product-image-container .hover-image {
  opacity: 0; 
}

.product-image-container:hover .hover-image {
  opacity: 1; 
}

.product-image-container:hover .first-image {
  opacity: 0; /
}

.product-slide h2.woocommerce-loop-product__title,
.product-slide .cta-div {
  position: relative;
  z-index: 2;
}

.title-stock-div {
  align-items: center;
  padding-top: 0px;
}

.product-info-slider {
  padding: 0px 16px;
}
.smort-heart-wrapper {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 999;
}
.woocommerce-product-details__short-description {
    margin: 0px !important;
}

/* Styling för WooCommerce variationer med variation-buttons */

/* Huvudcontainer för variationsformuläret */
.variations_form.cart {
  margin: 25px 0;
  padding: 20px;
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
}

/* Variationstabellen */
table.variations {
  width: 100%;
  margin-bottom: 20px;
  border-collapse: separate;
  border-spacing: 0;
}

table.variations td {
  vertical-align: top;
}

table.variations td.label {
  width: 20%;
  padding-right: 0;
  margin-bottom: 10px;
}

table.variations td.value {
  width: 100%;
}

/* Etikettstyling */
table.variations label {
  font-family: var(--fontFamily);
  font-size: 18px;
  text-transform: uppercase;
  font-weight: 600;
  color: var(--accentColor3);
  display: block;
}

/* Dölj select-elementet som används i bakgrunden */
table.variations select[style="display: none;"] {
  display: none !important;
}

/* Variation-buttons container */
.variation-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
  @media (width <= 540px) {
    grid-template-columns: repeat(1,1fr);
  }
  @media (1000px >= width >= 769px) {
    grid-template-columns: repeat(1,1fr);
  }
  @media (width >= 1400px) {
    grid-template-columns: repeat(3,1fr);
  }

}

/* Individuella variationsknappar */
.variation-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 45px;
  padding: 0 15px;
  background-color: #fff;
  border: 1px solid #333;
  font-size: 15px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.variation-button:hover {
  background-color: #f5f5f5;
}

/* Vald variationsknapp */
.variation-button.selected {
  background-color: #3f3c3c;
  color: white;
  border-color: #333;
}

/* Återställ variationer-länk */
.reset_variations {
  display: inline-block;
  margin-top: 15px;
  padding: 5px 10px;
  color: #666;
  font-size: 14px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
}

.reset_variations:hover {
  color: var(--accentColor);
  text-decoration: underline;
}

/* Single variation wrap */
.single_variation_wrap {
  margin-top: 20px;
}

/* Variationspris */
.woocommerce-variation-price {
  margin: 15px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--accentColor3);
}

.woocommerce-variation-price del {
  color: #999;
  font-size: 18px;
  margin-right: 8px;
}

.woocommerce-variation-price ins {
  text-decoration: none;
}

/* Variationsbeskrivning */
.woocommerce-variation-description {
  margin: 15px 0;
  font-size: 15px;
  line-height: 1.6;
  color: #666;
}

/* Tillgänglighet */
.woocommerce-variation-availability {
  margin: 15px 0;
}

.woocommerce-variation-availability p.stock {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 500;
  margin: 0;
}

.woocommerce-variation-availability p.stock.in-stock {
  color: #4CAF50;
}

.woocommerce-variation-availability p.stock.in-stock:before {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #4CAF50;
  border-radius: 50%;
  margin-right: 8px;
}

.woocommerce-variation-availability p.stock.out-of-stock {
  color: #F44336;
}

.woocommerce-variation-availability p.stock.out-of-stock:before {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  background-color: #F44336;
  border-radius: 50%;
  margin-right: 8px;
}

/* Kvantitetsväljare */
.quantity {
  display: flex;
  align-items: center;
  margin-top: 15px;
  margin-bottom: 15px;
}

.quantity .qty {
  width: 60px;
  height: 45px;
  border: 1px solid #e0e0e0;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  border-radius: 5px;
}


/* Animationer för variationsändringar */
.woocommerce-variation {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsiv styling */
@media (max-width: 768px) {
  .variation-buttons {
    /* grid-template-columns: repeat(2, 1fr); */
  }
  
  table.variations td.label,
  table.variations td.value {
    display: block;
    width: 100%;
    padding: 5px 0;
  }
  
  .single_add_to_cart_button {
    width: 100%;
    padding: 12px 20px !important;
    font-size: 16px !important;
  }
}

@media (max-width: 480px) {
  .variation-buttons {
    /* grid-template-columns: repeat(2, 1fr); */
  }
}


.article-number {
  font-size: 14px;
  color: #6e6e6e;
  margin-bottom: 10px;
  display: block;
}


/** Bundle **/

/* Bundle container */
.bundle_form .bundled_products {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 30px 0;
}

/* Varje bundled produkt som kort */
.bundled_product {
    background: #fff;
    padding-bottom: 10px !important;
    padding-top: 10px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid #f0f0f0;
    background-color: #f9f9f9;
    display: flex;
    justify-content: center;
    margin-bottom: 8px !important;
    cursor: pointer;
}

.bundled_product .details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left:0;
  
}

div.bundled_product_summary:not(.thumbnail_hidden) .details {
  padding: 0 !important;
}

.bundled_product:hover {
    transform: translateY(-2px);
}

/* Produktlayout inom kortet */
.bundled_product_content {
    display: flex;
    gap: 15px;
    align-items: flex-start;
    flex-direction: column;
}

/* Produktinfo sektion */
.bundled_product_info {
    display: flex;
    gap: 15px;
    align-items: center;
    width: 100%;
}

/* Bildsektion */
.bundled_product_images {
    flex: 0 0 80px;
}

.bundled_product_images img {
    width: 60px !important;
    height: 60px !important;
    object-fit: cover;
    border-radius: 8px;
}

/* Produktdetaljer */
.bundled_product_details {
    flex: 1;
}

.bundled_item_wrap span {
  color: black;
}

.bundled_product_title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

/* Prisvisning för bundled produkter */
.bundled_product .bundled_item_price,
.bundled_product .price {
    font-size: 16px;
    font-weight: 700;
    font-family: "CustomHeadingFont";
    margin-top: 10px;
    display: block !important;
    visibility: visible !important;
}

.bundled_product .price del {
    color: #999;
    font-size: 14px;
    margin-right: 5px;
}


.bundled_product .price ins {
    text-decoration: none;
    color: var(--accentColor);
}

/* Dölj inte priselement */
.bundled_product .bundled_item_price_visibility_hidden {
    display: block !important;
    visibility: visible !important;
}

.bundled_product_excerpt {
  display: none;
}

/* Tvinga fram prisvisning för bundled produkter */
.bundled-price,
.bundled-item-price-custom {
    display: block !important;
    visibility: visible !important;
    font-size: 16px;
    font-weight: 700;
    color: var(--accentColor3);
    font-family: "CustomHeadingFont";
    margin-top: 10px;
}

.bundled-price del,
.bundled-item-price-custom del {
    color: #999;
    font-size: 14px;
    margin-right: 5px;
}


.bundle_wrap{
  display: flex;
  width: 100%;
}

.bundled_product_summary .bundled_product_title a.bundled_product_permalink:before {
  color: var(--accentColor);
}


.bundle_button {
  display: flex;
  width: 100%;
}


/*** Checkout betalningssätt ***/

/* Checkout Payment Section Styling */
.woocommerce-checkout-payment {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    margin: 30px 0;
}

/* Payment Methods List */
.wc_payment_methods {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
}

/* Individual Payment Method */
.wc_payment_method {
    margin-bottom: 15px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 10px !important;
}

.wc_payment_method:hover {
    border-color: var(--accentColor);
    background: #fff;
}

/* Selected Payment Method */
.wc_payment_method:has(input[type="radio"]:checked) {
    border-color: var(--accentColor);
    background: #fff;
}

/* Payment Method Radio Button */
.wc_payment_method input[type="radio"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 50%;
    margin-right: 15px;
    position: relative;
    cursor: pointer;
    vertical-align: middle;
}

.wc_payment_method input[type="radio"]:checked {
    border-color: var(--accentColor);
    background: var(--accentColor);
}

.wc_payment_method input[type="radio"]:checked::after {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Payment Method Label */
.wc_payment_method label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    font-size: 18px;
    color: var(--accentColor3);
    font-family: "CustomContentFont";
    margin: 0;
}

/* Payment Method Icons */
.wc_payment_method img {
    margin-left: auto;
    max-height: 30px;
    width: auto;
}

/* Payment Box (beskrivning/formulär) */
.payment_box {
    margin-top: 15px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 5px;
    border-left: 4px solid var(--accentColor);
}

.payment_box p {
    margin: 0 0 10px 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Place Order Section */
.form-row.place-order {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #f0f0f0;
}

/* Terms and Conditions */
.woocommerce-terms-and-conditions-wrapper {
    margin-bottom: 20px;
}

.woocommerce-terms-and-conditions-checkbox-text {
    font-size: 16px;
    line-height: 1.5;
}

.woocommerce-terms-and-conditions-checkbox-text input[type="checkbox"] {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    accent-color: var(--accentColor);
}

/* Place Order Button */
#place_order {
    width: 100%;
    background: var(--accentColor);
    color: #fff;
    padding: 15px 30px;
    font-size: 18px;
    font-weight: bold;
    font-family: "CustomHeadingFont";
    text-transform: uppercase;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}


/* Responsive Design */
@media (max-width: 768px) {
    .woocommerce-checkout-payment {
        padding: 20px;
        margin: 20px 0;
    }
    
    .wc_payment_method {
        padding: 15px;
    }
    
    .wc_payment_method label {
        font-size: 14px;
    }
    
    #place_order {
        padding: 12px 20px;
        font-size: 16px;
    }
}

#add_payment_method #payment, .woocommerce-cart #payment, .woocommerce-checkout #payment {
  background-color: #f9f9f9;
}

/* Klarna "Välj ett annat betalsätt" knapp */
.checkout-button#klarna-checkout-select-other {
    display: inline-block;
    background: var(--accentColor);
    color: #ffff;
    padding: 15px 24px;
    font-size: 20px;
    font-weight: 600;
    font-family: "CustomHeadingFont";
    text-transform: uppercase;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    margin: 15px 0;
    text-align: center;
    cursor: pointer;
    width: 97%;
}

.checkout-button#klarna-checkout-select-other.orange-theme {
    border-color: var(--accentColor);
    color: var(--accentColor);
}

.checkout-button#klarna-checkout-select-other.orange-theme:hover {
    background: var(--accentColor);
    color: #fff;
    box-shadow: 0 4px 12px rgba(255, 94, 0, 0.3);
}

/* Responsiv design */
@media (max-width: 768px) {
    .checkout-button#klarna-checkout-select-other {
        width: 100%;
        padding: 14px 20px;
        font-size: 14px;
    }
}


