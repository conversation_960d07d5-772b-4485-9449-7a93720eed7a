<?php
get_header();
?>

<div class="error-404-container">
    <div class="error-404-content">
        <div class="error-404-number">404</div>
        <h1 class="error-404-title"><PERSON><PERSON> kunde inte hittas</h1>
        <p class="error-404-description">
            Tyvärr kunde vi inte hitta sidan du letar efter. Den kan ha flyttats, tagits bort eller så skrev du in fel adress.
        </p>

        <div class="error-404-actions">
            <a href="<?php echo home_url(); ?>" class="btn-primary">
                <i class="fas fa-home"></i>
                Tillbaka till startsidan
            </a>
            <a href="<?php echo wc_get_page_permalink('shop'); ?>" class="btn-secondary">
                <i class="fas fa-shopping-bag"></i>
                Besök vår butik
            </a>
        </div>

    </div>
</div>

<style>
    .error-404-container {
        min-height: 70vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem;


    }

    .error-404-content {
        max-width: 600px;
        text-align: center;
        background: white;
        padding: 3rem;

    }

    .error-404-number {
        font-size: 8rem;
        font-weight: bold;
        color: var(--accentColor, #007cba);
        line-height: 1;
        margin-bottom: 1rem;
        font-family: "CustomHeadingFont", Arial, sans-serif;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .error-404-title {
        font-size: 2.5rem;
        color: #333;
        margin-bottom: 1rem;
        font-family: "CustomHeadingFont", Arial, sans-serif;
    }

    .error-404-description {
        font-size: 1.2rem;
        color: #666;
        margin-bottom: 2rem;
        line-height: 1.6;
    }

    .error-404-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-bottom: 3rem;
        flex-wrap: wrap;
    }

    .btn-primary,
    .btn-secondary {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 12px 24px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        font-family: "CustomHeadingFont", Arial, sans-serif;
    }

    .btn-primary {
        background-color: var(--accentColor, #007cba);
        color: white;
    }

    .btn-primary:hover {

        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .btn-secondary {
        background-color: transparent;
        color: var(--accentColor2, #007cba);
        border: 2px solid var(--accentColor2, #007cba);
    }

    .btn-secondary:hover {


        transform: translateY(-2px);
    }

    /* Responsiv design */
    @media (max-width: 768px) {
        .error-404-content {
            padding: 2rem 1rem;
            margin: 1rem;
        }

        .error-404-number {
            font-size: 5rem;
        }

        .error-404-title {
            font-size: 2rem;
        }

        .error-404-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-primary,
        .btn-secondary {
            width: 100%;
            max-width: 250px;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .error-404-search .search-form {
            flex-direction: column;
        }
    }
</style>

<?php get_footer(); ?>