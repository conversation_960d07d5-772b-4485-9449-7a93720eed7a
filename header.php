<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- TrustBox script -->
    <script type="text/javascript" src="//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js" async></script>
    <!-- End TrustBox script -->
    <title><?php wp_title('|', true, 'right'); ?></title>
    <?php wp_head(); ?>
    <style>
        /* Normal header styles */
        .smort-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0 auto;
            width: calc(100% - 5%);
            padding: 5px 2.5% !important;
            background-color: #fafafa;
        }


        .sticky-header .wooj-icon-basket-1:before {
            filter: invert(1);
        }




        /* Sticky header styles */
        .sticky-header {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%) scale(0);
            width: 0;
            background-color: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
            padding: 10px 20px;
            transition: transform 0.2s ease-out, width 0.2s ease-out;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            opacity: 0;
            border-radius: 10px;
        }

        .sticky-header.active {
            transform: translateX(-50%) scale(1);
            /* Grow from center */
            width: 95%;
            /* Expand to full width */
            opacity: 1;
            /* Make visible */
        }

        img.modal-logo-mobile {
            width: 100%;
            position: absolute;
            bottom: -25%;
            left: 0;
        }

        .sticky-header .logo {
            display: flex;
            align-items: center;
        }

        .sticky-header .burger-menu {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            max-width: 50px;
            align-items: flex-end;
        }

        .sticky-header .burger-menu span {
            height: 2px;
            width: 100%;
            margin: 4px 0;
        }

        .sticky-header .burger-menu span {
            background: #fff;
        }

        .burger-menu.sticky-burger-menu span:nth-of-type(2) {
            width: 80%;
        }

        .right-sticky button {
            background: var(--accentColor);
            border-radius: 5px;
            color: #fff;
            border: 0px;
            width: 250px;
            text-align: left;
            font-family: var(--fontFamily);
            padding: 8px 10px;
            position: relative;
        }

        .right-sticky button:after {
            content: url('/wp-content/themes/smort_commerce/img/arrow-up-right.svg');
            position: absolute;
            right: 10px;
            filter: invert(1);
            transition: transform 0.2s ease-in-out;
        }

        .right-sticky button:hover:after {
            transform: rotate(45deg);
        }

        .right-sticky {
            display: flex;
            gap: 30px;
            width: 350px;
        }

        /* Preserve your button and other content */
        .sticky-header .smort-cart {
            display: flex;
            align-items: center;
        }

        .smort-footer {
            background-color: <?php echo get_field('secondary_color', 'option'); ?>;
        }

        .smort-nav ul li a:hover,
        .smort-nav ul li a:active {
            color: #fff;
        }

        .smort-logo img {
            height: <?php echo get_field('logo_height', 'option'); ?>px;
        }

        <?php if (get_field('transparent_header', 'option')): ?>.smort-header {
            background: none;
            position: absolute;
            width: calc(100% - 2%);
            padding: 1%;
        }

        body {
            margin-top: 0;
        }

        <?php endif; ?><?php if (get_field('enable_topbar', 'option')): ?>.smort-topbar {
            background-color: <?php echo get_field('topbar_background_color', 'option'); ?>;
            padding: 8px 2.5%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        <?php endif; ?>

        /* Mobile Menu Styles */
        .burger-menu-container {
            display: none;
            position: absolute;
            top: 0;
            left: 10px;
            width: 33%;
            display: flex;
            justify-content: flex-start !important;
            align-items: center;
        }

        .burger-menu {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            padding: 0px;
            max-width: 50px;
            position: initial;
            align-items: end;
        }

        .burger-menu span {
            background: var(--accentColor);
            height: 2px;
            width: 100%;
            margin: 4px 0;
        }

        .mobile-nav {
            display: none;
            position: fixed;
            top: 0;
            right: 0;
            width: 40%;
            height: 100%;
            z-index: 999999999999;
            justify-content: space-between;
            align-items: center;
            flex-direction: column;
            transition: opacity 0.3s ease-in-out;
            opacity: 0;
            pointer-events: none;
            margin-top: 0px;
            background: #000;
            /* Solid bakgrundsfärg */
            position: fixed;
        }

        .mobile-nav::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: url(/wp-content/uploads/2025/02/symbol-xk.png);
            background-repeat: no-repeat;
            background-size: cover;
            background-position: unset;
            opacity: 0.05;
            z-index: -1;
        }


        .mobile-nav.active {
            display: flex;
            opacity: 1;
            pointer-events: auto;
        }

        .mobile-nav-inner li a {
            font-size: 2.5rem !important;
            line-height: 1.2;
        }


        .mobile-nav-inner li a:hover {
            color: var(--accentColor);
        }

        .mobile-nav-inner li {
            text-align: left;
        }

        .mobile-nav-inner {
            text-align: center;
            width: 90%;
            overflow-y: auto;
            text-align: left;
            padding-top: 10%;
        }

        .close-nav {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            font-size: 30px;
            color: #fff;
            cursor: pointer;
            background: var(--accentColor3);
            padding: 10px 20px;
            border-radius: 50%;
        }

        .mobile-nav ul {
            list-style-type: none;
            padding: 0;
        }

        .mobile-nav ul li {
            margin: 10px 0;
        }

        .mobile-nav ul li a {
            color: #000;
            text-decoration: none;
            text-transform: uppercase;
            font-size: 20px;
            font-family: 'CustomHeadingFont';
        }

        .mobile-nav ul li ul {
            display: none;
            list-style-type: none;
            padding: 0;
        }

        .mobile-nav ul li.active>ul {
            display: block;
        }

        .sticky-header .logo img {
            filter: invert(1);
        }

        /* Navigation row styling */

        .navigation-row {
            max-width: 95vw;
            margin: 0 auto;
            padding: 10px;
            display: flex;
        }

        .nav-row-left {
            width: 100%;
        }


        @media (max-width: 992px) {

            .burger-menu-container,
            .smort-logo,
            .smort-cart {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                width: 48%;
                display: flex;
                align-items: center;
                position: relative;
                height: 50px;
            }

            .main-navigation {
                display: none;
            }

            .smort-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: calc(100% - 0px);
            }

            div#burger-menu span:nth-of-type(2) {
                width: 70%;
            }

            .burger-menu span {
                background: #000;
            }

            .burger-menu {
                align-items: flex-start;
            }

            .mobile-nav {
                justify-content: space-between;
                direction: ltr;
                padding-top: 0%;
                width: 100%;
            }

            .mobile-nav-inner li a {
                font-size: 2rem !important;
            }


            .burger-menu {
                z-index: 10 !important;
            }

            .menu-menu-1-container ul {
                text-align: left;
            }

            .contact-overlaymenu {
                padding-left: 5px !important;
            }

            ul.sub-menu a {
                font-size: 1.6rem !important;
            }

            .right-sticky button {
                display: none;
            }

            .right-sticky {
                justify-content: end;
            }

            .sticky-header img {
                height: 20px !important;
            }

            button#wc_search_trigger {
                display: none;
            }

            .sticky-header.active {
                width: 85%;
            }

            .smort-logo img {
                height: 35px;
            }

            ul.sub-menu.open {
                margin-left: 20px;
                margin-top: 20px;
                margin-bottom: 20px;
            }

            .navigation-row {
                display: none;
            }

            .smort-cart .language-currency-dropdown {
                display: none;
            }

            .smort-topbar.desktop {
                display: none;
            }

            .smort-header {
                padding: 5px 0px !important;
            }
        }



        .mobile-nav ul li {
            opacity: 0;
            transform: translateY(10px);
            animation: fadeInUp 0.5s ease forwards;
            animation-delay: var(--delay, 0s);
        }

        /* Animation for fading and sliding in */
        @keyframes fadeInUp {
            0% {
                opacity: 0;
                transform: translateY(10px);
            }

            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Apply a delay to each menu item */
        .mobile-nav.active ul li:nth-child(1) {
            --delay: 0.1s;
        }

        .mobile-nav.active ul li:nth-child(2) {
            --delay: 0.2s;
        }

        .mobile-nav.active ul li:nth-child(3) {
            --delay: 0.3s;
        }

        .mobile-nav.active ul li:nth-child(4) {
            --delay: 0.4s;
        }

        .mobile-nav.active ul li:nth-child(5) {
            --delay: 0.5s;
        }


        .smort-pristabell-features-extra li {
            position: relative;
            padding-left: 25px;
            background: url(/wp-content/uploads/2024/11/check-9.svg) no-repeat left center;
            background-size: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .forklaring-content {
            display: none;
            margin: 5px;
            padding: 0px 10px;
            border: 1px solid #828282;
            border-radius: 10px;
        }

        span.forklaring-toggle {
            position: absolute;
            right: 20px;
            cursor: pointer;
        }

        .forklaring-content p {
            font-size: 14px;
        }

        .mobile-nav-inner .menu-menu-1-container ul {
            border: 0px;
            text-align: left;
        }

        .menu-item-has-children>.menu-arrow {
            margin-left: 10px;
            cursor: pointer;
            display: inline-block;
        }

        .mobile-menu .sub-menu {
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .mobile-menu .sub-menu.open {
            display: block;
            opacity: 1;
        }

        img.menu-arrow {
            width: 30px !important;
            transform: rotate(-45deg);
            filter: brightness(0) invert(1);
        }

        .menu-arrow {
            margin-left: 10px;
            vertical-align: middle;
            /* Aligns the image with the text */
        }

        .mobile-nav-inner ul.sub-menu a {
            font-size: 2.5rem;
        }

        .contact-overlaymenu a {
            color: #fff;
            text-decoration: none;
            border: 1px solid #353535 !important;
            padding: 10px;
            border-radius: 0px;
            font-family: var(--fontFamily);
            width: 150px;
            text-align: center;
            font-weight: 600;
            text-transform: uppercase;
            background-color: var(--accentColor);
            font-size: 20px;


        }

        .contact-overlaymenu {
            margin-top: 15px;
            padding-left: 0px;
            margin-bottom: 50px;
            display: flex;
            gap: 20px;
        }

        .mobile-nav-logo {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100vw;
            height: auto;
            max-height: 40vh;
            /* Display only the top 60% of the image */
            overflow: hidden;
            z-index: 1001;
            /* Ensure it's above other elements */
            transform: translateY(40%);
            /* Show only the top 60% */
        }

        .mobile-nav-logo img {
            width: 100%;
            /* Make the image fill the width of the viewport */
            height: auto;
        }
    </style>


    <script>
        /*Sticky */

        document.addEventListener('DOMContentLoaded', function() {
            const header = document.querySelector('.smort-header');
            const stickyHeader = document.createElement('div');

            // Skapa och lägg till sticky-header
            stickyHeader.classList.add('sticky-header');
            stickyHeader.innerHTML = `
        <div class="logo">
            <a href="${window.location.origin}">
                <img src="/wp-content/uploads/2025/01/xenonkungen-logo-black.png" alt="Smort" style="height: 40px;">
            </a>
        </div>
        <div class="right-sticky">
            <a class="main-btn-xk" href="/shop">
                TILL PRODUKTERNA
            </a>
            <div class="burger-menu sticky-burger-menu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    `;
            document.body.appendChild(stickyHeader);

            // Hantera när sticky-header ska visas
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    stickyHeader.classList.add('active');
                } else {
                    stickyHeader.classList.remove('active');
                }
            });

            // Välj alla burger-menyer i headern och sticky-headern
            const burgerMenus = document.querySelectorAll('.burger-menu');
            const mobileNav = document.getElementById('mobile-nav');
            const closeNav = document.getElementById('close-nav');

            // Öppna mobilmenyn när en burger-meny klickas
            burgerMenus.forEach(menu => {
                menu.addEventListener('click', function() {
                    mobileNav.classList.add('active');
                });
            });

            // Stäng menyn när "X" klickas
            closeNav.addEventListener('click', function() {
                mobileNav.classList.remove('active');
            });

            // Stäng menyn när man klickar utanför den
            document.addEventListener('click', function(event) {
                if (!mobileNav.contains(event.target) && !event.target.closest('.burger-menu')) {
                    mobileNav.classList.remove('active');
                }
            });
        });



        /* Infinity scroll smort */
    </script>
</head>

<body <?php body_class(get_field('transparent_header', 'option') ? 'transparent-header' : ''); ?>>
    <?php if (get_field('enable_topbar', 'option')): ?>
        <div class="smort-topbar desktop">
            <div class="topbar-left">
                <ul class="usp-header">
                    <li><img src="/wp-content/uploads/2025/01/check-12.svg">Frakt från 49:-</li>
                    <li><img src="/wp-content/uploads/2025/01/check-12.svg">Fri frakt över 1995:-</li>
                    <li><img src="/wp-content/uploads/2025/01/check-12.svg">Öppet köp 30 dagar</li>
                    <li><img src="/wp-content/uploads/2025/01/check-12.svg">Snabb leverans</li>
                    <li><img src="/wp-content/uploads/2025/01/check-12.svg">Kundtjänst 0300-308 60</li>
                    <li><img src="/wp-content/uploads/2025/03/Klarna_Payment_Badge-1.svg">Säkra betalningar med Klarna</li>
                </ul>
            </div>

            <div class="topbar-right">
                <div class="tax-toggle-div">
                    <span>Pris</span>
                    <?php echo do_shortcode('[smort_tax_toggle]'); ?>
                </div>
                <a class="af-login" href="/mitt-konto">Återförsäljare / logga in</a>
            </div>
        </div>

    <?php endif; ?>
    <header class="smort-header smort-header-<?php echo get_field('header_style', 'option'); ?>">
        <?php if (get_field('header_style', 'option') == 'style_1'): ?>
            <div class="burger-menu-container">
                <div class="burger-menu" id="burger-menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
            <div class="smort-logo">
                <a href="<?php echo esc_url(home_url('/')); ?>">
                    <?php $logo = get_field('logo', 'option'); ?>
                    <?php if ($logo): ?>
                        <img src="<?php echo esc_url($logo); ?>" alt="<?php bloginfo('name'); ?>">
                    <?php else: ?>
                        Smort
                    <?php endif; ?>
                </a>
            </div>
            <nav class="main-navigation smort-nav smort-nav-centered">
                <?php echo do_shortcode('[swcs_search]'); ?>
            </nav>
            <div class="smort-cart">
                <?php echo do_shortcode('[custom_language_currency_dropdown]') ?>
                <a href="/mitt-konto"><img src="/wp-content/uploads/2025/01/user-7.svg"></a>
                <?php echo do_shortcode('[smort_wishlist_counter]') ?>
                <?php echo do_shortcode('[woo_j_cart_count]'); ?>
            </div>
        <?php endif; ?>


        <!-- Mobile Menu Elements -->
        <nav class="mobile-nav" id="mobile-nav">
            <div class="mobile-nav-inner">
                <div class="contact-overlaymenu">
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                    <a href="tel:+4630030860">0300 - 308 60</a>
                </div>
                <button class="close-nav" id="close-nav">&times;</button>
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'mobile-menu',
                    'menu_class'     => 'mobile-menu',
                    'container'      => false,
                ));
                ?>
            </div>

            <div class="sub-menu-xk">
                <a href="/kopvillkor">Villkor</a>
                <a href="/integritetspolicy">Integritetspolicy</a>

            </div>
            <div class="tax-toggle-div">
                <?php echo do_shortcode('[custom_language_currency_dropdown]'); ?>
                <span>Pris</span>
                <?php echo do_shortcode('[smort_tax_toggle]'); ?>
            </div>
        </nav>
        <div>
    </header>



    <div class="navigation-row">
        <div class="nav-row-left">
            <nav class="main-navigation smort-nav smort-nav-left">
                <?php
                wp_nav_menu(array(
                    'smort_commerce' => 'header-menu',
                    'menu_class' => 'menu',
                    'container' => false,
                ));
                ?>
            </nav>
        </div>
        <div class="burger-menu sticky-burger-menu">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>

    <script>
        /* Script E-handelsförfrågan */



        document.addEventListener('DOMContentLoaded', function() {
            const openModal = document.getElementById('openModal');
            const modal = document.getElementById('bookJobModal');
            const closeModal = modal.querySelector('.close-modal');

            // Open modal on button click
            openModal.addEventListener('click', function() {
                modal.style.display = 'flex';
            });

            // Close modal on close button click
            closeModal.addEventListener('click', function() {
                modal.style.display = 'none';
            });

            // Close modal when clicking outside of modal content
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });
    </script>


</body>

</html>