<?php
// functions.php

function enqueue_custom_styles_scripts()
{
    wp_enqueue_style('child-theme-css', get_stylesheet_directory_uri() . '/style.css', array(), '1.0', 'all');
    wp_enqueue_style('single-product-css', get_stylesheet_directory_uri() . '/single-product.css', array(), '1.0', 'all');
    wp_enqueue_style('archive-product-css', get_stylesheet_directory_uri() . '/archive-product.css', array(), '1.0', 'all');
    wp_enqueue_script('custom-accordion', get_stylesheet_directory_uri() . '/js/custom-accordion.js', array('jquery'), '1.0', true);
    wp_enqueue_script('custom-variation-script', get_template_directory_uri() . '/js/custom-variation-script.js', array('jquery'), false, true);
}
add_action('wp_enqueue_scripts', 'enqueue_custom_styles_scripts');



function register_acf_block_types()
{

    // Logos scroll Smort Hero block
    acf_register_block_type(array(
        'name'                    => 'kundloggorscroll',
        'title'                     => __('Kundloggor - Scroll'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/logos-scroll/logos-scroll.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/logos-scroll/logos-scroll.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/logos-scroll/logos-scroll.js',
    ));
    // Product slider - Smort   
    acf_register_block_type(array(
        'name'                    => 'Produkt slider - Smort  ',
        'title'                     => __('Produkt slider - Smort'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/product-slider-smort/product-slider-smort.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/product-slider-smort/product-slider-smort.js',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/product-slider-smort/product-slider-smort.css',
    ));
    acf_register_block_type(array(
        'name'                    => 'Smort - Kategorier',
        'title'                     => __('Smort - Kategorier karusell'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/smort-cat-carousel/smort-cat-carousel.js',
    ));
    acf_register_block_type(array(
        'name'                    => 'Video / text - Smort Block',
        'title'                     => __('Video / text - Smort Block'),
        'description'          => __('Smort block'),
        'render_template'    => '/template-parts/smortblocks/video-text/video-text.php',
        'category'              => 'smort',
        'icon'                    => 'id',
        'keywords'              => array('CTA', 'Block'),
        'mode'            => 'edit',
        'enqueue_style'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/video-text/video-text.css',
        'enqueue_script'     => get_stylesheet_directory_uri() . '/template-parts/smortblocks/video-text/video-text.js',
    ));
}
// Check if function exists and hook into setup.
if (function_exists('acf_register_block_type')) {
    add_action('acf/init', 'register_acf_block_types');
}


function enqueue_swiper_slider()
{
    // Enqueue Swiper CSS
    wp_enqueue_style('swiper-css', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css', array(), '10.0.0');

    // Enqueue Swiper JS
    wp_enqueue_script('swiper-js', 'https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js', array(), '10.0.0', true);
}
add_action('wp_enqueue_scripts', 'enqueue_swiper_slider');



// Allow SVG file uploads in WordPress
function allow_svg_uploads($mimes)
{
    // Add SVG mime type
    $mimes['svg'] = 'image/svg+xml';
    $mimes['svgz'] = 'image/svg+xml';
    return $mimes;
}
add_filter('upload_mimes', 'allow_svg_uploads');

// Sanitize SVG files for security
function sanitize_svg_upload($file)
{
    $file_type = wp_check_filetype($file['name']);
    if ($file_type['ext'] === 'svg' || $file_type['ext'] === 'svgz') {
        $file['type'] = 'image/svg+xml';
    }
    return $file;
}
add_filter('wp_check_filetype_and_ext', 'sanitize_svg_upload', 10, 4);

// Display SVG previews in the media library
function display_svg_in_media_library($response, $attachment, $meta)
{
    if ($response['type'] === 'image' && $response['subtype'] === 'svg+xml' && class_exists('SimpleXMLElement')) {
        $svg_path = get_attached_file($attachment->ID);
        if (file_exists($svg_path)) {
            $svg_content = file_get_contents($svg_path);
            $xml = simplexml_load_string($svg_content);
            if ($xml !== false) {
                $svg_content = $xml->asXML();
                $response['image'] = [
                    'src' => 'data:image/svg+xml;base64,' . base64_encode($svg_content),
                    'width' => (int) $xml['width'],
                    'height' => (int) $xml['height'],
                ];
            }
        }
    }
    return $response;
}
add_filter('wp_prepare_attachment_for_js', 'display_svg_in_media_library', 10, 3);


// Remove the default WooCommerce ordering dropdown
remove_action('woocommerce_before_shop_loop', 'woocommerce_catalog_ordering', 30);

// Remove the result count text
remove_action('woocommerce_before_shop_loop', 'woocommerce_result_count', 20);


// Remove the default "Add to cart" button in the loop
remove_action('woocommerce_after_shop_loop_item', 'woocommerce_template_loop_add_to_cart', 10);

//remove add to cart




/* USP under varukorg */

// Function to add content below the add to cart form
function custom_content_below_add_to_cart_form()
{
    echo '<ul class="custom-features-list" style="list-style: none; padding: 0; margin: 10px 0; display: flex; justify-content: center; gap: 10px; flex-wrap: wrap;">';
    echo '<li style="display: inline-block;"><span style="color: var(--accentColor);">✓</span> Snabba leveranser</li>';
    echo '<li style="display: inline-block;"><span style="color: var(--accentColor);">✓</span> Säkra betalningar</li>';
    echo '<li style="display: inline-block;"><span style="color: var(--accentColor);">✓</span> Ångerrätt 30 dagar</li>';
    echo '</ul>';
}
add_action('woocommerce_after_add_to_cart_form', 'custom_content_below_add_to_cart_form');



function onskelista_produktsidor()
{
    echo '<div class="heart-btn-smort">';
    echo do_shortcode('[smort_wishlist_button]');
    echo '</div>';
}
add_action('woocommerce_after_add_to_cart_form', 'onskelista_produktsidor');





function woocommerce_share()
{
    echo '<div class="woocommerce-share">';
    echo '<h3 class="share-title">Dela på</h3>'; // Rubrik för delningssektionen
    echo '<a href="https://www.facebook.com/sharer/sharer.php?u=' . get_the_permalink() . '" target="_blank" class="wp-block-social-link-anchor"><i class="fab fa-facebook-f"></i></a>';
    echo '<a href="https://twitter.com/intent/tweet?url=' . get_the_permalink() . '&text=' . get_the_title() . '" target="_blank" class="share-twitter"><i class="fab fa-x-twitter"></i></a>'; // Ändrat till X
    echo '<a href="https://www.linkedin.com/shareArticle?mini=true&url=' . get_the_permalink() . '&title=' . get_the_title() . '" target="_blank" class="share-linkedin"><i class="fab fa-linkedin-in"></i></a>';
    echo '</div>';
}
add_action('woocommerce_single_product_summary', 'woocommerce_share', 35);


/*Font awesome */
function enqueue_font_awesome()
{
    wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css', array(), '6.5.1');
}
add_action('wp_enqueue_scripts', 'enqueue_font_awesome');


add_theme_support('wc-product-gallery-zoom');
add_theme_support('wc-product-gallery-lightbox');
add_theme_support('wc-product-gallery-slider');

function enqueue_slick_slider()
{
    // Enqueue jQuery (already included by WordPress by default, but ensuring it's loaded)
    wp_enqueue_script('jquery');

    // Enqueue Slick Slider CSS
    wp_enqueue_style('slick-css', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css', array(), '1.8.1');

    // Enqueue Slick Slider Theme CSS (optional)
    wp_enqueue_style('slick-theme-css', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css', array(), '1.8.1');

    // Enqueue Slick Slider JS
    wp_enqueue_script('slick-js', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js', array('jquery'), '1.8.1', true);

    // Enqueue your custom Slick initialization script
}
add_action('wp_enqueue_scripts', 'enqueue_slick_slider');


// Produkter läs mer

function add_read_more_button_to_summary()
{
    global $post;
    if (!empty($post->post_excerpt)) { ?>
        <a class="las-mer-summary" href="#tabs-content">Läs mer</a>
    <?php }
}
add_action('woocommerce_single_product_summary', 'add_read_more_button_to_summary', 21);




/* Shortcode anställda */


function visa_anstallda_grid_shortcode()
{
    ob_start();

    // Definiera alla fält där anställda kan vara lagrade
    $field_names = array('anstallda_sverige');

    $anstallda_hittades = false;

    ?>
    <div class="anstallda-grid-container">
        <?php
        // Loopa genom alla fält och hämta anställda
        foreach ($field_names as $field_name) {
            if (have_rows($field_name, 'option')) {
                while (have_rows($field_name, 'option')) : the_row();
                    $anstallda_hittades = true;
                    $bild = get_sub_field('anstalld_bild');
                    $namn = get_sub_field('anstalld_namn');
                    $roll = get_sub_field('anstalld_roll');
                    $telefonnummer = get_sub_field('anstalld_telefonnummer');
                    $epost = get_sub_field('anstalld_epost');
        ?>
                    <div class="anstalld-item">
                        <?php if ($bild) : ?>
                            <img src="<?php echo esc_url($bild['url']); ?>" alt="<?php echo esc_attr($namn); ?>" class="anstalld-bild">
                        <?php endif; ?>

                        <?php if ($namn) : ?>
                            <h3 class="anstalld-namn"><?php echo esc_html($namn); ?></h3>
                        <?php endif; ?>

                        <?php if ($roll) : ?>
                            <p class="anstalld-roll"><?php echo esc_html($roll); ?></p>
                        <?php endif; ?>

                        <div class="anstalld-kontakt">
                            <?php if ($telefonnummer) : ?>
                                <p><a href="tel:<?php echo esc_attr($telefonnummer); ?>"><?php echo esc_html($telefonnummer); ?></a></p>
                            <?php endif; ?>
                            <?php if ($epost) : ?>
                                <p><a href="mailto:<?php echo esc_attr($epost); ?>"><?php echo esc_html($epost); ?></a></p>
                            <?php endif; ?>
                        </div>
                    </div>
        <?php
                endwhile;
            }
        }
        ?>
    </div>
    <?php

    if (!$anstallda_hittades) {
        echo '<p>Inga anställda hittades.</p>';
    }

    return ob_get_clean();
}
add_shortcode('smort_anstallda', 'visa_anstallda_grid_shortcode');


/* Custom post type - Ambassadör */

// Registrera Custom Post Type "Ambassadörer"
function create_ambassador_cpt()
{
    $labels = array(
        'name'                  => 'Ambassadörer',
        'singular_name'         => 'Ambassadör',
        'menu_name'             => 'Ambassadörer',
        'name_admin_bar'        => 'Ambassadör',
        'add_new'               => 'Lägg till ny',
        'add_new_item'          => 'Lägg till ny Ambassadör',
        'new_item'              => 'Ny Ambassadör',
        'edit_item'             => 'Redigera Ambassadör',
        'view_item'             => 'Visa Ambassadör',
        'all_items'             => 'Alla Ambassadörer',
        'search_items'          => 'Sök Ambassadörer',
        'not_found'             => 'Inga Ambassadörer hittades',
    );

    $args = array(
        'labels'                => $labels,
        'public'                => true,
        'publicly_queryable'    => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'query_var'             => true,
        'rewrite'               => array('slug' => 'ambassador'),
        'capability_type'       => 'post',
        'has_archive'           => true,
        'hierarchical'          => false,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-id',
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'show_in_rest'          => true, // Gör den synlig i blockredigeraren
    );

    register_post_type('ambassador', $args);
}

add_action('init', 'create_ambassador_cpt');

// Shortcode för att visa ambassadörer i en grid
function ambassadorer_grid_shortcode($atts)
{
    ob_start();

    $query = new WP_Query(array(
        'post_type'      => 'ambassador',
        'posts_per_page' => -1, // Ladda alla ambassadörer
        'orderby'        => 'date',
        'order'          => 'DESC'
    ));

    if ($query->have_posts()) {
        echo '<div class="ambassadors-grid">';
        while ($query->have_posts()) {
            $query->the_post();
            $background_image = get_the_post_thumbnail_url(get_the_ID(), 'large');
            $title = get_the_title();
            $permalink = get_permalink();

    ?>
            <a href="<?php echo esc_url($permalink); ?>" class="ambassador-link">
                <div class="ambassador-item" style="background-image: url('<?php echo esc_url($background_image); ?>');">
                    <div class="overlay"></div>
                    <div class="content">
                        <h2><?php echo esc_html($title); ?></h2>
                        <span class="read-more-btn">Läs mer</span>
                    </div>
                </div>
            </a>
    <?php
        }
        echo '</div>';
        wp_reset_postdata();
    } else {
        echo '<p>Inga ambassadörer hittades.</p>';
    }

    return ob_get_clean();
}

add_shortcode('ambassador_grid', 'ambassadorer_grid_shortcode');


add_filter('woocommerce_my_account_my_orders_actions', 'bbloomer_order_again_action', 9999, 2);

function bbloomer_order_again_action($actions, $order)
{
    if ($order->has_status('completed')) {
        $actions['order-again'] = array(
            'url' => wp_nonce_url(add_query_arg('order_again', $order->get_id(), wc_get_cart_url()), 'woocommerce-order_again'),
            'name' => __('Order again', 'woocommerce'),
        );
    }
    return $actions;
}




/* Filter area widget */

function custom_theme_widgets_init()
{
    // Register a sidebar (widget area)
    register_sidebar(array(
        'name'          => 'Filter',
        'id'            => 'filter',
        'description'   => 'Product Sidebar',
        'before_widget' => '<div class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ));
}
add_action('widgets_init', 'custom_theme_widgets_init');

function custom_order_notes_accordion()
{
    ?>
    <div class="order-notes-container">
        <button id="toggle-notes-btn" class="toggle-notes">+</button>
        <label for="order_comments"><?php _e('Beställningsanteckningar (Valfritt)', 'woocommerce'); ?></label>
        <div id="order-notes-content" class="hidden">
            <textarea name="order_comments" id="order_comments" class="input-text" placeholder="<?php _e('Anteckningar för din beställning, t.ex. speciella önskemål för leverans.', 'woocommerce'); ?>"></textarea>
        </div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const toggleBtn = document.getElementById("toggle-notes-btn");
            const notesContent = document.getElementById("order-notes-content");

            toggleBtn.addEventListener("click", function() {
                if (notesContent.classList.contains("hidden")) {
                    notesContent.classList.remove("hidden");
                    toggleBtn.textContent = "−";
                } else {
                    notesContent.classList.add("hidden");
                    toggleBtn.textContent = "+";
                }
            });
        });
    </script>

    <style>
        .order-notes-container {
            margin-top: 20px;
            border: 0px;
            padding: 15px;
            border-radius: 0px;
            background: #f9f9f9;
            position: relative;
            font-family: 'CustomHeadingFont';
            text-transform: uppercase;
            font-size: 20px;
        }

        .toggle-notes {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--accentColor);
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            font-size: 20px;
            cursor: pointer;
            border-radius: 50%;
        }

        #order-notes-content.hidden {
            display: none;
        }

        #order_comments {
            width: 100%;
            min-height: 80px;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ccc;
        }
    </style>
<?php
}
add_action('woocommerce_after_order_notes', 'custom_order_notes_accordion');

// Hantera AJAX-förfrågan för senast besökta produkter
function get_recently_viewed_products_ajax()
{
    // Kontrollera att product_ids finns
    if (!isset($_POST['product_ids'])) {
        wp_send_json_error('No product IDs provided');
        return;
    }

    // Hämta och validera produkt-ID:n
    $product_ids = json_decode(stripslashes($_POST['product_ids']), true);

    if (!is_array($product_ids) || empty($product_ids)) {
        wp_send_json_error('Invalid product IDs');
        return;
    }

    // Validera att produkterna existerar
    $valid_products = [];
    foreach ($product_ids as $product_id) {
        if (wc_get_product($product_id)) {
            $valid_products[] = $product_id;
        }
    }

    if (empty($valid_products)) {
        wp_send_json_error('No valid products found');
        return;
    }

    // Hämta produkterna
    $args = array(
        'post_type'      => 'product',
        'post__in'       => $valid_products,
        'orderby'        => 'post__in',
        'posts_per_page' => 10,
    );

    $recently_viewed_query = new WP_Query($args);

    ob_start();

    if ($recently_viewed_query->have_posts()) {
        while ($recently_viewed_query->have_posts()) {
            $recently_viewed_query->the_post();
            echo '<div class="swiper-slide product-slide">';
            wc_get_template_part('content', 'product');
            echo '</div>';
        }
    }

    wp_reset_postdata();

    $html = ob_get_clean();

    wp_send_json_success($html);
}
add_action('wp_ajax_get_recently_viewed_products', 'get_recently_viewed_products_ajax');
add_action('wp_ajax_nopriv_get_recently_viewed_products', 'get_recently_viewed_products_ajax');


/* Artikelnummer single product*/
function single_product_article_number()
{
    global $product;
    $article_number = $product->get_sku();
    if (! empty($article_number)) {
        echo '<span class="article-number">Art.nr: ' . esc_html($article_number) . '</span>';
    }
}
add_action('woocommerce_single_product_summary', 'single_product_article_number', 4);




// AJAX handler för uppdatering av fraktkostnad
function handle_update_shipping_method()
{
    // Verifiera nonce för säkerhet
    if (!wp_verify_nonce($_POST['nonce'], 'update_shipping_nonce')) {
        wp_die('Security check failed');
    }

    if (!isset($_POST['shipping_method'])) {
        wp_send_json_error('No shipping method provided');
    }

    $shipping_method = sanitize_text_field($_POST['shipping_method']);

    // Säkerställ att WooCommerce är laddat
    if (! WC()->session) {
        wp_send_json_error('WooCommerce session not available');
    }

    // Uppdatera vald fraktmetod i session
    WC()->session->set('chosen_shipping_methods', array($shipping_method));

    // Tvinga omberäkning av frakt
    WC()->cart->calculate_shipping();
    WC()->cart->calculate_totals();

    // Hämta uppdaterade fraktpaket
    $packages = WC()->shipping->get_packages();
    $shipping_cost = __('Free', 'woocommerce');

    if (! empty($packages)) {
        foreach ($packages as $i => $package) {
            if (isset($package['rates'][$shipping_method])) {
                $method = $package['rates'][$shipping_method];
                $cost = floatval($method->cost);
                $taxes = array_sum($method->taxes);
                $total_cost = $cost + $taxes;

                if ($total_cost > 0) {
                    $shipping_cost = wc_price($total_cost);
                } else {
                    $shipping_cost = __('Free', 'woocommerce');
                }
                break;
            }
        }
    }

    // Hämta uppdaterad total kostnad
    $cart_total = WC()->cart->get_total();

    // Debug information
    error_log('Shipping method updated: ' . $shipping_method);
    error_log('Shipping cost: ' . $shipping_cost);
    error_log('Cart total: ' . $cart_total);

    // Skicka tillbaka data
    wp_send_json_success(array(
        'shipping_cost' => $shipping_cost,
        'total' => $cart_total,
        'debug' => array(
            'method' => $shipping_method,
            'packages_count' => count($packages)
        )
    ));
}

// Registrera AJAX-hanterare för både inloggade och icke-inloggade användare
add_action('wp_ajax_update_shipping_method', 'handle_update_shipping_method');
add_action('wp_ajax_nopriv_update_shipping_method', 'handle_update_shipping_method');

// Ändra text på Klarna knappen omedelbart
function change_klarna_button_text()
{
?>
    <script>
        jQuery(document).ready(function($) {
            function updateKlarnaButtonText() {
                var klarnaButton = $('#klarna-checkout-select-other');
                if (klarnaButton.length && klarnaButton.text().trim() !== 'Faktura') {
                    klarnaButton.text('Faktura');
                }
            }

            // Kör omedelbart
            updateKlarnaButtonText();

            // Använd MutationObserver för att övervaka ändringar
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList' || mutation.type === 'characterData') {
                        updateKlarnaButtonText();
                    }
                });
            });

            // Övervaka hela dokumentet för ändringar
            observer.observe(document.body, {
                childList: true,
                subtree: true,
                characterData: true
            });

            // Backup med kortare intervall
            setInterval(updateKlarnaButtonText, 100);
        });
    </script>
<?php
}
add_action('wp_footer', 'change_klarna_button_text');
